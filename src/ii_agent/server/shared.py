from ii_agent.core.config.ii_agent_config import config
from dotenv import load_dotenv

from ii_agent.core.storage import get_file_store
from ii_agent.core.storage.settings.file_settings_store import FileSettingsStore
from ii_agent.server.services.sandbox_service import SandboxService
from ii_agent.server.services.agent_service import AgentService
from ii_agent.server.services.message_service import MessageService
from ii_agent.server.services.session_service import SessionService
from ii_agent.server.websocket.manager import ConnectionManager
from ii_agent.server.services.file_service import FileService
from ii_agent.storage import create_storage_client


load_dotenv()


file_store = get_file_store(config.file_store, config.file_store_path)

# Create service layer
sandbox_service = SandboxService(
    config=config,
)

agent_service = AgentService(
    config=config,
    file_store=file_store,
)

storage = create_storage_client(
    config.storage_provider,
    config.file_upload_project_id,
    config.file_upload_bucket_name,
)

file_service = FileService(
    storage=storage,
)

message_service = MessageService(
    agent_service=agent_service,
    config=config,
    file_service=file_service,
)

session_service = SessionService(
    agent_service=agent_service,
    message_service=message_service,
    sandbox_service=sandbox_service,
    file_store=file_store,
    config=config,
)

connection_manager = ConnectionManager(
    session_service=session_service,
    config=config,
)

SettingsStoreImpl = FileSettingsStore
