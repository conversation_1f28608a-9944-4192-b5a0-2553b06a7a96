"""MCP settings management API endpoints."""

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException
from typing import Optional

from ii_agent.db.models import User
from ii_agent.server.api.deps import SessionDep, get_db_session, DBSession
from ii_agent.server.auth.middleware import CurrentUser, get_current_user
from ii_agent.server.mcp_settings.models import (
    MCPSettingCreate,
    MCPSettingUpdate,
    MCPSettingInfo,
    MCPSettingList,
)
from ii_agent.server.mcp_settings.service import (
    create_mcp_settings,
    update_mcp_settings,
    get_mcp_settings,
    get_active_mcp_settings,
    list_mcp_settings,
    delete_mcp_settings,
)


router = APIRouter(prefix="/user-settings/mcp", tags=["User MCP Settings Management"])


@router.post("/", response_model=MCPSettingInfo)
async def create_mcp_setting(
    setting: MCPSettingCreate,
    current_user: CurrentUser,
    db: SessionDep,
):
    """Create or update MCP settings for the current user."""

    result = await create_mcp_settings(
        db_session=db,
        mcp_setting_in=setting,
        user_id=current_user.id,
    )

    return result


@router.get("/", response_model=MCPSettingList)
async def list_user_mcp_settings(
    only_active: bool = False,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """List all MCP settings for the current user."""

    return await list_mcp_settings(
        db_session=db,
        user_id=current_user.id,
        only_active=only_active,
    )


@router.get("/active", response_model=Optional[MCPSettingInfo])
async def get_active_mcp_setting(
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """Get the active MCP settings for the current user."""

    return await get_active_mcp_settings(
        db_session=db,
        user_id=current_user.id,
    )


@router.get("/{setting_id}", response_model=MCPSettingInfo)
async def get_mcp_setting(
    setting_id: str,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """Get specific MCP settings by ID."""

    mcp_setting = await get_mcp_settings(
        db_session=db,
        setting_id=setting_id,
        user_id=current_user.id,
    )

    if not mcp_setting:
        raise HTTPException(status_code=404, detail="MCP settings not found")

    return mcp_setting


@router.put("/{setting_id}", response_model=MCPSettingInfo)
async def update_mcp_setting(
    setting_id: str,
    setting_update: MCPSettingUpdate,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """Update existing MCP settings."""

    updated_setting = await update_mcp_settings(
        db_session=db,
        setting_id=setting_id,
        setting_update=setting_update,
        user_id=current_user.id,
    )

    if not updated_setting:
        raise HTTPException(status_code=404, detail="MCP settings not found")

    return updated_setting


@router.delete("/{setting_id}")
async def delete_mcp_setting(
    setting_id: str,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """Delete MCP settings by ID."""

    deleted = await delete_mcp_settings(
        db_session=db,
        setting_id=setting_id,
        user_id=current_user.id,
    )

    if not deleted:
        raise HTTPException(status_code=404, detail="MCP settings not found")

    return {"message": "MCP settings deleted successfully"}
