"""MCP settings management Pydantic models."""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List


class MCPSettingCreate(BaseModel):
    """Model for creating/updating MCP settings."""

    mcp_config: Dict[str, Any] = Field(..., description="MCP configuration object")


class MCPSettingUpdate(BaseModel):
    """Model for updating existing MCP settings."""

    mcp_config: Optional[Dict[str, Any]] = Field(
        None, description="MCP configuration object"
    )
    is_active: Optional[bool] = Field(
        None, description="Whether the MCP setting is active"
    )


class MCPSettingInfo(BaseModel):
    """Model for MCP setting information."""

    id: str
    mcp_config: Dict[str, Any]
    is_active: bool
    created_at: str
    updated_at: Optional[str] = None


class MCPSettingList(BaseModel):
    """Model for MCP setting list response."""

    settings: List[MCPSettingInfo]

    def get_by_id(self, setting_id: str) -> Optional[MCPSettingInfo]:
        """Get MCP setting by ID."""
        return next(
            (setting for setting in self.settings if setting.id == setting_id),
            None,
        )
