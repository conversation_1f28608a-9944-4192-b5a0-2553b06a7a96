import asyncio
import logging
import uuid
from typing import Dict, Optional, List

import socketio

from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.server.services.session_service import SessionService
from ii_agent.server.websocket.chat_session import ChatSession

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages Socket.IO connections and their associated chat sessions."""

    def __init__(
        self,
        session_service: Optional[SessionService],
        config: IIAgentConfig,
    ):
        # Active chat sessions mapped by session UUID
        self.sessions: Dict[uuid.UUID, ChatSession] = {}
        self.session_service = session_service
        self.config = config
        # Track which socket IDs belong to which sessions
        self.socket_to_session: Dict[str, uuid.UUID] = {}
        self.sio: Optional[socketio.AsyncServer] = None
        # Track sessions being cleaned up to prevent race conditions
        self.session_locks: Dict[uuid.UUID, asyncio.Lock] = {}
    
    def set_sio(self, sio: socketio.AsyncServer):
        self.sio = sio

    async def connect(
        self, sid: str, session_uuid_str: Optional[str] = None, user_id: Optional[str] = None
    ) -> ChatSession:
        """Accept a new Socket.IO connection and create or join a chat session.

        Args:
            sid: Socket.IO session ID
            session_uuid_str: Optional session UUID string
            user_id: Optional authenticated user ID

        Returns:
            ChatSession instance
        """
        if not self.sio:
            raise RuntimeError("Socket.IO server not initialized")
        # Get session UUID from params or generate new one
        if session_uuid_str:
            session_uuid = uuid.UUID(session_uuid_str)
        else:
            session_uuid = uuid.uuid4()

        # Track socket to session mapping
        self.socket_to_session[sid] = session_uuid
        
        # Join the Socket.IO room for this session
        try:
            await self.sio.enter_room(sid, str(session_uuid))
            logger.info(f"Socket {sid} joined room {session_uuid}")
        except ValueError as e:
            logger.error(f"Failed to join room {session_uuid} for socket {sid}: {e}")
            # Clean up the socket mapping since joining failed
            if sid in self.socket_to_session:
                del self.socket_to_session[sid]
            raise

        # Check if session already exists
        if session_uuid not in self.session_locks:
            self.session_locks[session_uuid] = asyncio.Lock()

        async with self.session_locks[session_uuid]:
            if session_uuid in self.sessions:
                # Add socket to existing session
                session = self.sessions[session_uuid]
                session.add_socket(sid)
                logger.info(
                    f"Socket.IO client joined existing session {session_uuid} for user {user_id}: {sid}"
                )
            else:
                # Create a new chat session using the session service
                if not self.session_service:
                    raise RuntimeError("SessionService not initialized")
            
                session = self.session_service.create_session(
                    sockets=[sid],
                    session_uuid=session_uuid,
                    user_id=user_id,
                    sio=self.sio,
                )
                
                self.sessions[session_uuid] = session
                
                logger.info(
                    f"New chat session {session_uuid} created for user {user_id}: {sid}"
                )
            return session

    async def disconnect(self, sid: str):
        """Handle Socket.IO disconnection and cleanup."""
        logger.info(f"Socket.IO client disconnecting: {sid}")

        if not self.sio:
            raise RuntimeError("Socket.IO server not initialized")

        if sid not in self.socket_to_session:
            logger.warning(f"Socket.IO client disconnecting without session mapping: {sid}")
            return

        #NEED THREAD PROTECTION
        session_uuid = self.socket_to_session[sid]
        del self.socket_to_session[sid]

        if session_uuid in self.sessions:
            session = self.sessions[session_uuid]
            session.remove_socket(sid)
            
            # Leave the Socket.IO room
            try:
                await self.sio.leave_room(sid, str(session_uuid))
            except ValueError as e:
                logger.warning(f"Failed to leave room {session_uuid} for socket {sid}: {e}")
                # Continue with cleanup even if leaving room fails
            
            # If no more sockets in session, clean up the session
            if not session.sockets:
                if session_uuid not in self.session_locks:
                    self.session_locks[session_uuid] = asyncio.Lock()
                # If still have task, wait till task done
                if session.has_active_task():
                    logger.info(f"Clean up session {session_uuid} - wait for active task to finish")
                    await session.active_task
                async with self.session_locks[session_uuid]:
                    # Re-check if session still has no sockets (could have changed during task wait)
                    if not session.sockets and session_uuid in self.sessions:
                        # Clean up shared resources
                        await session.cleanup()
                        del self.sessions[session_uuid]
                        logger.info(f"Session {session_uuid} closed - no more connections")
                    elif session_uuid not in self.sessions:
                        logger.info(f"Already cleaned up session {session_uuid}. Skipping...")
                    else:
                        logger.info(f"New socket joined session {session_uuid}. Skipping...")
    
    def get_session(self, sid: str, session_uuid_str: Optional[str] = None) -> Optional[ChatSession]:
        """Get the chat session for a Socket.IO connection."""
        if sid in self.socket_to_session:
            session_uuid = self.socket_to_session[sid]
            return self.sessions.get(session_uuid)
        if session_uuid_str:

            try:
                session_uuid = uuid.UUID(session_uuid_str)
                return self.sessions.get(session_uuid)
            except ValueError:
                logger.error(f"Invalid session UUID: {session_uuid_str}")
        return None
    
    def get_connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.sessions)
