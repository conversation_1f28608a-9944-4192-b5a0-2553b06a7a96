from typing import List
from dataclasses import dataclass
from ii_agent.db.manager import Files
from ii_agent.storage import ObjectStorage


@dataclass
class FileData:
    id: str
    name: str
    content_type: str
    url: str
    

class FileService:
    
    def __init__(self, storage: ObjectStorage):
        self.storage = storage

    async def get_file_by_id(self, file_id: str) -> FileData:
        file = await Files.get_file_by_id(file_id)
        if not file:
            raise FileNotFoundError(f"File with id {file_id} not found")
        
        signed_url = self.storage.get_download_signed_url(file.storage_path)
    
        return FileData(
            id=file.id,
            name=file.file_name,
            content_type=file.content_type,
            url=signed_url,
        )

    async def get_files_by_session_id(self, session_id: str) -> List[FileData]:
        files = await Files.get_files_by_session_id(session_id)
        if not files:
            raise FileNotFoundError(f"No files found for session {session_id}")
        
        return [
            FileData(
                id=file.id,
                name=file.file_name,
                content_type=file.content_type,
                url=self.storage.get_download_signed_url(file.storage_path),
            )
            for file in files
        ]