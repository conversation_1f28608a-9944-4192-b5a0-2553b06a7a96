from ii_agent.llm.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LL<PERSON>lient, TextPrompt, TextResult
from ii_agent.llm.context_manager.base import ContextManager
from ii_agent.llm.token_counter import Token<PERSON>ounter
from ii_agent.utils.constants import TOKEN_BUDGET, SUMMARY_MAX_TOKENS
from ii_agent.core.logger import logger


class LLMCompact(ContextManager):
    def __init__(
        self,
        client: LLMClient,
        token_counter: TokenCounter,
        token_budget: int = TOKEN_BUDGET,
    ):
        super().__init__(token_counter=token_counter, token_budget=token_budget)
        self.client = client
        self.token_counter = token_counter

    def apply_truncation(
        self, message_lists: list[list[GeneralContentBlock]]
    ) -> list[list[GeneralContentBlock]]:
        """Apply compact truncation by generating a summary of the entire conversation."""
        if len(message_lists) <= 1:
            return message_lists

        # Create summary request by appending COMPACT_PROMPT as user message
        summary_request_message = [TextPrompt(text=COMPACT_PROMPT)]
        messages_for_summary = message_lists + [summary_request_message]

        # Generate summary using LLM
        try:
            # Use simple system prompt for summarization
            model_response, _ = self.client.generate(
                messages=messages_for_summary,
                max_tokens=SUMMARY_MAX_TOKENS,
                thinking_tokens=0,
                system_prompt="You are a helpful AI assistant tasked with summarizing conversations.",
            )

            # Extract summary text from response
            summary_text = ""
            for message in model_response:
                if isinstance(message, TextResult):
                    summary_text += message.text

            if not summary_text:
                logger.warning("No summary text generated, using fallback")
                summary_text = "Conversation summary could not be generated."

            logger.info(
                f"Generated compact summary for {len(message_lists)} message lists"
            )

        except Exception as e:
            logger.error(f"Failed to generate compact summary: {e}")
            summary_text = f"Failed to generate summary due to error: {str(e)}"

        # Return user message about compact command + summary as assistant message
        user_message = [TextPrompt(text=f"You’re working on a long horizon task for me; below is the context you previously wrote summarizing progress so far. {summary_text}. Please continue your work!")]
        return [user_message]

COMPACT_USER_MESSAGE = "Use the /compact command to clear the conversation history, and start a new conversation with the summary in context."

COMPACT_PROMPT = """
Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing development work without losing context.

Before providing your final summary, wrap your analysis in <analysis> tags to organize your thoughts and ensure you've covered all necessary points. In your analysis process:

1. Chronologically analyze each message and section of the conversation. For each section thoroughly identify:
   - The user's explicit requests and intents
   - Your approach to addressing the user's requests
   - Key decisions, technical concepts and code patterns
   - Specific details like file names, full code snippets, function signatures, file edits, etc
2. Double-check for technical accuracy and completeness, addressing each required element thoroughly.

Your summary should include the following sections:

1. Primary Request and Intent: Capture all of the user's explicit requests and intents in detail
2. Key Technical Concepts: List all important technical concepts, technologies, and frameworks discussed.
3. Files and Code Sections: Enumerate specific files and code sections examined, modified, or created. Pay special attention to the most recent messages and include full code snippets where applicable and include a summary of why this file read or edit is important.
4. Problem Solving: Document problems solved and any ongoing troubleshooting efforts.
5. Pending Tasks: Outline any pending tasks that you have explicitly been asked to work on.
6. Current Work: Describe in detail precisely what was being worked on immediately before this summary request, paying special attention to the most recent messages from both user and assistant. Include file names and code snippets where applicable.
7. Optional Next Step: List the next step that you will take that is related to the most recent work you were doing. IMPORTANT: ensure that this step is DIRECTLY in line with the user's explicit requests, and the task you were working on immediately before this summary request. If your last task was concluded, then only list next steps if they are explicitly in line with the users request. Do not start on tangential requests without confirming with the user first.
                       If there is a next step, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no drift in task interpretation.

Here's an example of how your output should be structured:

<example>
<analysis>
[Your thought process, ensuring all points are covered thoroughly and accurately]
</analysis>

<summary>
1. Primary Request and Intent:
   [Detailed description]

2. Key Technical Concepts:
   - [Concept 1]
   - [Concept 2]
   - [...]

3. Files and Code Sections:
   - [File Name 1]
      - [Summary of why this file is important]
      - [Summary of the changes made to this file, if any]
      - [Important Code Snippet]
   - [File Name 2]
      - [Important Code Snippet]
   - [...]

4. Problem Solving:
   [Description of solved problems and ongoing troubleshooting]

5. Pending Tasks:
   - [Task 1]
   - [Task 2]
   - [...]

6. Current Work:
   [Precise description of current work]

7. Optional Next Step:
   [Optional Next step to take]

</summary>
</example>

Please provide your summary based on the conversation so far, following this structure and ensuring precision and thoroughness in your response. 

There may be additional summarization instructions provided in the included context. If so, remember to follow these instructions when creating the above summary. Examples of instructions include:
<example>
## Compact Instructions
When summarizing the conversation focus on typescript code changes and also remember the mistakes you made and how you fixed them.
</example>

<example>
# Summary instructions
When you are using compact - please focus on test output and code changes. Include file reads verbatim.
</example>
"""
