
from ii_tool.sandbox.providers.base import Sandbox
from ii_tool.core.workspace import WorkspaceManager
from ii_tool.tools.dev.database import GetDatabaseConnection

from ii_tool.core.config import (
    DatabaseConfig,
)


from ii_tool.tools.shell import (
    ShellInit,
    ShellRunCommand,
    ShellView,
    ShellKill,
    ShellStopCommand,
    ShellList,
    TmuxSessionManager,
)
from ii_tool.tools.file_system import (
    GlobTool,
    GrepTool,
    LSTool,
    FileReadTool,
    FileWriteTool,
    FileEditTool,
    MultiEditTool,
)
from ii_tool.tools.productivity import TodoReadTool, TodoWriteTool
from ii_tool.tools.media import (
    VideoGenerateTool,
    ImageGenerateTool,
)
from ii_tool.tools.dev import FullStackInitTool, RegisterPort
from ii_tool.tools.web import WebSearchTool, WebVisitTool, ImageSearchTool


def get_common_tools(
    sandbox: Sandbox,
    database_config: DatabaseConfig,
):
    tools = [
        # Sandbox tools
        RegisterPort(sandbox=sandbox),
        # Todo tools
        TodoReadTool(),
        TodoWriteTool(),
        # Database tools
        GetDatabaseConnection(setting=database_config),
    ]

    return tools

def get_sandbox_tools(workspace_path: str, tool_server_url: str):
    terminal_manager = TmuxSessionManager()
    workspace_manager = WorkspaceManager(workspace_path)

    tools = [
        # Shell tools
        ShellInit(terminal_manager, workspace_manager),
        ShellRunCommand(terminal_manager),
        ShellView(terminal_manager),
        ShellKill(terminal_manager),
        ShellStopCommand(terminal_manager),
        ShellList(terminal_manager),
        # File system tools
        GlobTool(workspace_manager),
        GrepTool(workspace_manager),
        LSTool(workspace_manager),
        FileReadTool(workspace_manager),
        FileWriteTool(workspace_manager),
        FileEditTool(workspace_manager),
        MultiEditTool(workspace_manager),
        FullStackInitTool(workspace_manager),
        # Media tools
        ImageGenerateTool(tool_server_url),
        VideoGenerateTool(tool_server_url),
        # Web tools
        WebSearchTool(tool_server_url),
        WebVisitTool(tool_server_url),
        ImageSearchTool(tool_server_url),
    ]

    return tools
