import httpx
from typing import Any
from ii_tool.tools.base import BaseTool, ToolResult

# Name
NAME = "web_visit"
DISPLAY_NAME = "Web Visit"

# Tool description
DESCRIPTION = "You should call this tool when you need to visit a webpage and extract its content. Returns webpage content as text."

# Input schema
INPUT_SCHEMA = {
        "type": "object",
        "properties": {
            "url": {
                "type": "string",
                "description": "The url of the webpage to visit.",
            }
        },
        "required": ["url"],
    }


class WebVisitTool(BaseTool):
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = True

    def __init__(self, tool_server_url: str):
        super().__init__()
        self.tool_server_url = tool_server_url

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        url = tool_input["url"]
        if "arxiv.org/abs" in url:
            url = "https://arxiv.org/html/" + url.split("/")[-1]

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.tool_server_url}/web-visit",
                json={"url": url},
            )
            response.raise_for_status()
            response_data = response.json()

        content = response_data["content"]

        return ToolResult(
            llm_content=content,
        )


    async def execute_mcp_wrapper(self, url: str):
        return await self._mcp_wrapper(
            tool_input={
                "url": url,
            }
        )