import json
import httpx
from typing import Any
from ii_tool.tools.base import BaseTool, ToolResult


# Name
NAME = "web_search"
DISPLAY_NAME = "Web Search"

# Tool description
DESCRIPTION = """Performs a web search using a search engine API and returns the search results."""

# Input schema
INPUT_SCHEMA = {
    "type": "object",
    "properties": {
        "query": {"type": "string", "description": "The search query to perform."},
    },
    "required": ["query"],
}

MAX_RESULTS = 5
    
class WebSearchTool(BaseTool):
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = True

    def __init__(self, tool_server_url: str):
        super().__init__()
        self.tool_server_url = tool_server_url

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        query = tool_input["query"]

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.tool_server_url}/web-search",
                json={"query": query},
            )
            response.raise_for_status()
            response_data = response.json()

        results = response_data["results"][:MAX_RESULTS]

        results_str = json.dumps(results, indent=2)
        
        # TODO: custom the user display content
        return ToolResult(
            llm_content=results_str,
        )

    async def execute_mcp_wrapper(self, query: str):
        return await self._mcp_wrapper(
            tool_input={
                "query": query,
            }
        )