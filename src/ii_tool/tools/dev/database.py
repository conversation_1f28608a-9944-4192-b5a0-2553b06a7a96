from typing import Any, Optional
from ii_tool.core.config import DatabaseConfig
from ii_tool.tools.base import BaseTool, ToolResult
from ii_tool.tools.dev.clients.database_client import get_database_client

# Name
NAME = "get_database_connection"
DISPLAY_NAME = "Get database connection"

# Tool description
DESCRIPTION = """\
Get a database connection.
"""

# Input schema
INPUT_SCHEMA = {
    "type": "object",
    "properties": {
        "database_type": {
            "type": "string",
            "description": "Type of the database to connect to",
            "enum": ["postgres"],
        },
    },
    "required": ["database_type"],
}

class GetDatabaseConnection(BaseTool):
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = False

    def __init__(
        self,
        setting: DatabaseConfig,
    ) -> None:
        super().__init__()
        self.setting = setting

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        database_type = tool_input["database_type"]
        
        try:
            connection_string = get_database_client(database_type, self.setting).get_database_connection()
        except Exception as e:
            return ToolResult(
                llm_content=f"Failed to get database connection. Error: {str(e)}",
                user_display_content=f"Failed to get database connection. Error: {str(e)}",
                is_error=True,
            )
        return ToolResult(
            llm_content=f"Successfully got database connection. Tool output: {connection_string}",
            user_display_content=f"Successfully got database connection. Tool output: {connection_string}",
            is_error=False,
        )

    async def execute_mcp_wrapper(
        self,
        database_type: str,
    ):
        return await self._mcp_wrapper(
            tool_input={
                "database_type": database_type,
            }
        )
