import os

from mcp.types import ToolAnnotations
from fastmcp import FastMCP
from argparse import ArgumentParser
from ii_tool.tools.manager import get_sandbox_tools
from dotenv import load_dotenv

load_dotenv()


async def create_mcp(workspace_dir: str, tool_server_url: str):    
    tools = get_sandbox_tools(
        workspace_path=workspace_dir,
        tool_server_url=tool_server_url,
    )

    mcp = FastMCP()

    for tool in tools:
        mcp.tool(
            tool.execute_mcp_wrapper,
            name=tool.name,
            description=tool.description,
            annotations=ToolAnnotations(
                title=tool.display_name,
                readOnlyHint=tool.read_only,
            ),
        )

        # NOTE: this is a temporary fix to set the parameters of the tool
        _mcp_tool = await mcp._tool_manager.get_tool(tool.name)
        _mcp_tool.parameters = tool.input_schema

    return mcp

async def main():
    parser = ArgumentParser()
    parser.add_argument("--workspace_dir", type=str, default=None)
    parser.add_argument("--tool_server_url", type=str, default=None)
    parser.add_argument("--port", type=int, default=6060)
    
    args = parser.parse_args()

    workspace_dir = os.getenv("WORKSPACE_DIR")
    if args.workspace_dir:
        workspace_dir = args.workspace_dir

    if not workspace_dir:
        raise ValueError("workspace_dir is not set. Please set the WORKSPACE_DIR environment variable or pass it as an argument --workspace_dir")

    os.makedirs(workspace_dir, exist_ok=True)

    tool_server_url = args.tool_server_url
    if not tool_server_url:
        tool_server_url = os.getenv("TOOL_SERVER_URL")
        if not tool_server_url:
            raise ValueError("tool_server_url is not set. Please set the TOOL_SERVER_URL environment variable or pass it as an argument --tool_server_url")

    mcp = await create_mcp(workspace_dir=workspace_dir, tool_server_url=tool_server_url)
    await mcp.run_async(transport="http", host="0.0.0.0", port=args.port)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())