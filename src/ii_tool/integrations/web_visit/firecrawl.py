import aiohttp

from .base import BaseWebVisitClient, WebVisitError


class FireCrawlWebVisitClient(BaseWebVisitClient):
    """FireCrawl implementation of web visit client."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.firecrawl.dev/v1/scrape"

    async def extract(self, url: str) -> str:
        """Visit webpage and extract content using FireCrawl."""

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }
        payload = {
            "url": url,
            "onlyMainContent": False,
            "formats": ["markdown"],
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.base_url, headers=headers, json=payload,
            ) as response:
                response.raise_for_status()
                response_data = await response.json()

        data = response_data.get("data", {}).get("markdown", "")
        if not data:
            raise WebVisitError(
                "No content could be extracted from webpage"
            )

        return data