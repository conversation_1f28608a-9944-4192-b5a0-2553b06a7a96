import urllib
import aiohttp

from typing import Any, List, Dict
from .base import BaseImageSearchClient, ImageSearchError


class SerpAPIImageSearchClient(BaseImageSearchClient):
    """SerpAPI implementation of image search client."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://serpapi.com/search.json"

    async def search(self, query: str) -> List[Dict[str, Any]]:
        params = {
            "q": query,
            "api_key": self.api_key,
            "engine": "google_images",
        }
        encoded_url = f"{self.base_url}?{urllib.parse.urlencode(params)}"

        async with aiohttp.ClientSession() as session:
            async with session.get(encoded_url) as response:
                if response.status != 200:
                    raise ImageSearchError(
                        f"SerpAPI request failed: {response.status} {response.reason}"
                    )

                data = await response.json()
        
        results = data.get("images_results", [])

        search_response = [
            {
                "title": result.get("title", ""),
                "image_url": result.get("original", ""),
                "width": result.get("original_width", ""),
                "height": result.get("original_height", ""),
            }
            for result in results
        ]

        return search_response