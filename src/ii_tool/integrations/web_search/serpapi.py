import urllib
import aiohttp

from typing import List, Dict
from .base import BaseWebSearchClient, WebSearchError


class SerpAPIWebSearchClient(BaseWebSearchClient):
    """SerpAPI implementation of web search client."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://serpapi.com/search.json"

    async def search(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        params = {
            "q": query,
            "api_key": self.api_key,
            "num": min(max_results, 100),
        }
        encoded_url = f"{self.base_url}?{urllib.parse.urlencode(params)}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(encoded_url) as response:
                if response.status != 200:
                    raise WebSearchError(
                        f"SerpAPI request failed: {response.status} {response.reason}"
                    )

                data = await response.json()

        results = data.get("organic_results", [])

        search_response = [
            {
                "title": result.get("title", ""),
                "url": result.get("link", ""),
                "content": result.get("snippet", "")
            }
            for result in results
        ]

        return search_response