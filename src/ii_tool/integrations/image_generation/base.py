from abc import ABC, abstractmethod
from typing import Literal


class ImageGenerationError(Exception):
    """Custom exception for image generation errors."""
    pass


class BaseImageGenerationClient(ABC):
    """Base interface for image generation clients."""
    
    @abstractmethod
    async def generate_image(
        self,
        prompt: str,
        aspect_ratio: Literal["1:1", "16:9", "9:16", "4:3", "3:4"] = "1:1",
        **kwargs,
    ) -> str:
        """Generate an image based on the provided text prompt."""
        pass