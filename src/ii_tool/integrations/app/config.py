from pydantic_settings import BaseSettings, SettingsConfigDict
from ii_tool.core.config import (
    ImageGenerateConfig,
    ImageSearchConfig,
    VideoGenerateConfig,
    WebSearchConfig,
    WebVisitConfig,
)


class ToolServerConfig(BaseSettings):
    web_search_config: WebSearchConfig = WebSearchConfig()
    web_visit_config: WebVisitConfig = WebVisitConfig()
    image_search_config: ImageSearchConfig = ImageSearchConfig()
    video_generate_config: VideoGenerateConfig = VideoGenerateConfig()
    image_generate_config: ImageGenerateConfig = ImageGenerateConfig()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",
    )

config = ToolServerConfig()