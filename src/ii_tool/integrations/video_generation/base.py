from abc import ABC, abstractmethod
from typing import Literal


class BaseVideoGenerationClient(ABC):
    """Base interface for video generation clients."""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """Initialize the client with provider-specific configuration."""
        pass
    
    @abstractmethod
    async def generate_video(
        self,
        prompt: str,
        aspect_ratio: Literal["16:9", "9:16"] = "16:9",
        duration_seconds: int = 5,
        image_base64: str | None = None,
        image_mime_type: str | None = None,
    ) -> str:
        """Generate video from text prompt or/and image."""
        pass