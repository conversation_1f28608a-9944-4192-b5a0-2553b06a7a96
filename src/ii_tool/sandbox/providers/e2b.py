"""E2B sandbox provider implementation."""

from datetime import datetime, timezone
import logging
from typing import <PERSON><PERSON>, AsyncIterator, Literal, Optional

from e2b_code_interpreter import AsyncSandbox
from ii_tool.sandbox.config import SandboxConfig
from ii_tool.sandbox.providers.base import Sandbox
from ii_tool.sandbox.sandbox_queue import SandboxQueueScheduler

logger = logging.getLogger(__name__)

DEFAULT_TIMEOUT = 3600

class E2BSandbox(Sandbox):
    """E2B sandbox provider for managing remote code execution environments."""

    def __init__(self, sandbox: AsyncSandbox, sandbox_id: str, queue: SandboxQueueScheduler):
        super().__init__()
        self._sandbox = sandbox
        self._sandbox_id = sandbox_id
        self._queue = queue

    def _ensure_sandbox(self):
        if not self._sandbox and not self._sandbox_id:
            raise ValueError("Sandbox not created")       

    @property
    def provider_sandbox_id(self):
        try: 
            self._ensure_sandbox()
            return self._sandbox.sandbox_id
        except Exception as e:
            raise Exception(f"Failed to get provider sandbox ID {e}")
    
    @property
    def sandbox_id(self):
        return self._sandbox_id
    
    @classmethod
    def _ensure_credentials(cls, config: SandboxConfig):
        if not config.e2b_api_key or not config.e2b_template_id:
            raise ValueError("E2B API key and template ID are required")


    @classmethod
    async def create(
        cls,
        config: SandboxConfig,
        queue: SandboxQueueScheduler,
        sandbox_id: str,
    ):
        try:
            cls._ensure_credentials(config)
            sandbox = await AsyncSandbox.create(
                config.e2b_template_id,
                api_key=config.e2b_api_key,
            )
            instance = cls(
                sandbox,
                sandbox_id=sandbox_id,
                queue=queue,
            )
            await instance._set_timeout(config.timeout_seconds)
            return instance
        except Exception as e:
            raise Exception(f"Failed to create sandbox {e}")
    
    @classmethod
    async def resume(
        cls,
        provider_sandbox_id: str,
        config: SandboxConfig,
        queue: SandboxQueueScheduler,
        sandbox_id: str,
    ):
        try:
            cls._ensure_credentials(config)
            sandbox = await AsyncSandbox.resume(
                provider_sandbox_id,
                api_key=config.e2b_api_key,
            )
            instance = cls(
                sandbox,
                sandbox_id=sandbox_id,
                queue=queue,
            )
            await instance._set_timeout(config.timeout_seconds)
            return instance
        except Exception as e:
            raise Exception(f"Failed to resume sandbox {e}")



    @classmethod
    async def delete(cls, provider_sandbox_id: str, config: SandboxConfig, queue: SandboxQueueScheduler, sandbox_id: str):
        """Delete a sandbox instance."""
        try:
            cls._ensure_credentials(config)
            sandbox = await AsyncSandbox.connect(provider_sandbox_id, api_key=config.e2b_api_key)
            await sandbox.kill()
            await queue.cancel_message(sandbox_id)
        except Exception as e:
            raise Exception(f"Failed to delete sandbox {e}")

    @classmethod
    async def stop(cls, provider_sandbox_id: str, config: SandboxConfig, queue: 'SandboxQueueScheduler', sandbox_id: str):
        try:
            cls._ensure_credentials(config)
            sandbox = await AsyncSandbox.connect(provider_sandbox_id, api_key=config.e2b_api_key)
            await sandbox.pause()
            await queue.cancel_message(sandbox_id)
        except Exception as e:
            raise Exception(f"Failed to stop sandbox {e}")
    
    @classmethod
    async def schedule_timeout(cls, provider_sandbox_id: str, sandbox_id: str, config: SandboxConfig, queue: SandboxQueueScheduler, timeout_seconds: int):
        try:
            cls._ensure_credentials(config)
            sandbox = cls(
                await AsyncSandbox.connect(provider_sandbox_id, api_key=config.e2b_api_key),
                sandbox_id=sandbox_id,
                queue=queue,
            )
            await sandbox._set_timeout(timeout_seconds)
        except Exception as e:
            raise Exception(f"Failed to schedule timeout for sandbox {provider_sandbox_id}: {e}")

    @classmethod
    async def connect(
        cls,
        provider_sandbox_id: str,
        config: SandboxConfig,
        queue: SandboxQueueScheduler,
        sandbox_id: str,
    ) -> 'E2BSandbox':
        """Create a new sandbox instance."

        Returns:
            Sandbox ID from E2B
        """
        try:
            cls._ensure_credentials(config)
            sandbox = cls(
                await AsyncSandbox.connect(
                    provider_sandbox_id,
                    api_key=config.e2b_api_key,
                ),
                sandbox_id=sandbox_id,
                queue=queue,
            )
            await sandbox._set_timeout(config.timeout_seconds)
            return sandbox
        except Exception as e:
            raise Exception(f"Failed to create sandbox {e}")
    
    async def _set_timeout(self, timeout: int = DEFAULT_TIMEOUT):
        try:
            TIMEOUT_AFTER_PAUSE_SECONDS = 600
            # Actual timeout and delete the sandbox
            await self._sandbox.set_timeout(timeout + TIMEOUT_AFTER_PAUSE_SECONDS)
            
            # Schedule timeout with queue if available
            if self._queue and self._sandbox_id:
                # PAUSE BEFORE TIMEOUT WITH QUEUE
                await self._queue.schedule_message(
                    sandbox_id=self._sandbox_id,
                    action="pause",
                    delay_seconds=timeout,
                    metadata={
                        "reason": "idle", 
                        "created_at": datetime.now(timezone.utc).isoformat()
                    }
                )
                logger.info(f"Scheduled timeout for sandbox {self._sandbox_id} in {timeout // 60} minutes")
        except Exception as e:
            raise Exception(f"Failed to extend timeout {e}")

    async def get_host(self) -> str:
        try:
            self._ensure_sandbox()
            return f"{self.provider_sandbox_id}.{self._sandbox.connection_config.domain}"
        except Exception as e:
            raise Exception(f"Failed to get host {e}")
    
    async def expose_port(self, port: int) -> str:
        try:
            self._ensure_sandbox()
            return f"https://{self._sandbox.get_host(port)}"
        except Exception as e:
            raise Exception(f"Failed to expose port {e}")


    async def read_file(self, file_path: str) -> str:
        """Read a file from the sandbox.

        Args:
            file_path: Path to the file in the sandbox

        Returns:
            File content as string
        """
        try:
            self._ensure_sandbox()
            return await self._sandbox.files.read(file_path, format="text")
        except Exception as e:
            raise Exception(f"Failed to read file {e}")

    async def write_file(
        self, file_content: str | bytes | IO, file_path: str
    ):
        """Write content to a file in the sandbox.

        Args:
            file_content: Content to write
            file_path: Path to the file in the sandbox

        Returns:
            True if written successfully
        """
        try:
            self._ensure_sandbox()
            await self._sandbox.files.write(file_path, file_content)
        except Exception as e:
            raise Exception(f"Failed to write file {e}")

    async def upload_file(self, file_content: str | bytes | IO, remote_file_path: str) -> bool:
        """Upload a file to the sandbox.

        Args:
            file_content: Content of the file
            remote_file_path: Path to the file in the sandbox

        Returns:
            True if uploaded successfully
        """
        try:
            self._ensure_sandbox()
            if await self._sandbox.files.exists(remote_file_path):
                logger.error(f"File {remote_file_path} already exists")
                return False
            await self._sandbox.files.write(remote_file_path, file_content)
            return True
        except Exception as e:
            raise Exception(f"Failed to upload file {e}")

    async def delete_file(self, file_path: str) -> bool:
        """Delete a file from the sandbox.

        Args:
            file_path: Path to the file in the sandbox

        Returns:
            True if deleted successfully
        """
        try:
            self._ensure_sandbox()
            await self._sandbox.files.remove(file_path)
            return True
        except Exception as e:
            raise Exception(f"Failed to delete file {e}")
        

    async def download_file(self, remote_file_path: str, format: Literal["text", "bytes", "stream"] = "text") -> Optional[str | bytearray | AsyncIterator[bytes]]:
        """Download a file from the sandbox.

        Args:
            remote_file_path: Path to the file in the sandbox            
            format: Format of the file content ("text", "bytes", or "stream")

        Returns:
            File content as string, bytes, or iterator of bytes
        """
        try:
            self._ensure_sandbox()
            content = await self._sandbox.files.read(path = remote_file_path, format = format)
            return content
        except Exception as e:
            raise Exception(f"Failed to download file {e}")
    
    async def cancel_timeout(self):
        """Cancel any scheduled timeout for this sandbox."""
        if self._queue and self._sandbox_id:
            try:
                await self._queue.cancel_message(self._sandbox_id)
            except Exception as e:
                logger.error(f"Failed to cancel timeout for sandbox {self._sandbox_id}: {e}")

    async def create_directory(self, directory_path: str, exist_ok: bool = False) -> bool:
        """Create a directory in the sandbox.

        Args:
            directory_path: Path to the directory in the sandbox

        Returns:
            True if created successfully
        """
        try:
            self._ensure_sandbox()
            exist = await self._sandbox.files.make_dir(directory_path)
            if not exist and not exist_ok:
                raise Exception(f"Directory {directory_path} already exists")
            return True
        except Exception as e:
            raise Exception(f"Failed to create directory {e}")
