# Installation guide

# Frontend

## Create .env 

copy from /home/<USER>/ii-agent-prod/test/ii-agent-prod/frontend/.env

```
VITE_API_URL=http://localhost:8000 #Or in our case https://api-agent.ii.inc
VITE_GOOGLE_CLIENT_ID=
```

## Package installation
```
bun install
bun run build
```

## Start script

```
bun run preview
```


# Backend

## Installation script and change environment (Same with conda)

```
uv venv --python 3.10
uv pip install -e . --prerelease=allow
source .venv/bin/activate
```

## Start script

Copy from /home/<USER>/ii-agent-prod/test/ii-agent-prod/start.sh

and run 

```
bash start.sh
```

## start.sh

```
#AUTH
export GOOGLE_CLIENT_ID=
export GOOGLE_CLIENT_SECRET=
export OAUTHLIB_INSECURE_TRANSPORT=1
export GOOGLE_DISCOVERY_URL=
export ACCESS_TOKEN_EXPIRE_MINUTES=

#LLM CONFIG
export LLM_CONFIGS='{"gemini/gemini-2.5-pro":{"model":"gemini-2.5-pro","api_key":"FILL IN API KEY","base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"vertex/claude-sonnet-4@20250514":{"model":"claude-sonnet-4@20250514","api_key":null,"base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":"us-east5","vertex_project_id":"backend-alpha-97077","api_type":"anthropic","thinking_tokens":10000,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"default":{"model":"gemini-2.5-pro","api_key":"FILL IN API KEY","base_url":null,"max_retries":10,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false}}'

#TOOL CONFIG
export AUTO_APPROVE_TOOLS=true
#IMAGE
export IMAGE_SEARCH_SERPAPI_API_KEY=
#SEARCH
export WEB_SEARCH_SERPAPI_API_KEY=
export WEB_VISIT_FIRECRAWL_API_KEY=
#DATABASE
export DATABASE_NEON_DB_API_KEY=
#SANDBOX AND LIFECYCLE
export SANDBOX_E2B_API_KEY=
export SANDBOX_E2B_TEMPLATE_ID=
export SANDBOX_REDIS_URL=

#Application Database. Local for now. But can get a cloud connection later
export DATABASE_URL="sqlite+aiosqlite:///./ii_agent.db"

python ws_server.py --port 9999

```
