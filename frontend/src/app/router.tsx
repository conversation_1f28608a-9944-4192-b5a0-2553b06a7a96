import { createBrowser<PERSON>outer, Router<PERSON>rovider } from 'react-router'
import { ProtectedRoute } from '@/components/protected-route'
import { RootLayout } from '@/components/layouts/root-layout'

const createAppRouter = () =>
    createBrowserRouter([
        {
            path: '/',
            element: <RootLayout />,
            children: [
                {
                    index: true,
                    async lazy() {
                        const { Component } = await import('@/app/routes/home')
                        return {
                            Component: () => (
                                <ProtectedRoute>
                                    <Component />
                                </ProtectedRoute>
                            )
                        }
                    }
                },
                {
                    path: 'login',
            async lazy() {
                const { AuthLayout } = await import('@/components/layouts/auth-layout')
                return { Component: AuthLayout }
            },
            children: [
                {
                    index: true,
                    lazy: () => import('@/app/routes/login')
                }
            ]
        },
                {
                    path: 'signup',
            async lazy() {
                const { AuthLayout } = await import('@/components/layouts/auth-layout')
                return { Component: AuthLayout }
            },
            children: [
                {
                    index: true,
                    lazy: () => import('@/app/routes/signup')
                }
            ]
        },
                {
                    path: 'forgot-password',
            async lazy() {
                const { AuthLayout } = await import('@/components/layouts/auth-layout')
                return { Component: AuthLayout }
            },
            children: [
                {
                    index: true,
                    lazy: () => import('@/app/routes/forgot-password')
                }
            ]
        },
                {
                    path: 'terms',
            async lazy() {
                const { PublicLayout } = await import('@/components/layouts/public-layout')
                return { Component: PublicLayout }
            },
            children: [
                {
                    index: true,
                    lazy: () => import('@/app/routes/terms-of-use')
                }
            ]
        },
                {
                    path: 'privacy',
            async lazy() {
                const { PublicLayout } = await import('@/components/layouts/public-layout')
                return { Component: PublicLayout }
            },
            children: [
                {
                    index: true,
                    lazy: () => import('@/app/routes/privacy-policy')
                }
            ]
        },
                {
                    path: 'dashboard',
            async lazy() {
                const { Component } = await import('@/app/routes/dashboard')
                return {
                    Component: () => (
                        <ProtectedRoute>
                            <Component />
                        </ProtectedRoute>
                    )
                }
            }
        },
                {
                    path: 'settings',
            async lazy() {
                const { Component } = await import('@/app/routes/settings')
                return {
                    Component: () => (
                        <ProtectedRoute>
                            <Component />
                        </ProtectedRoute>
                    )
                }
            }
        },
                {
                    path: ':sessionId',
            async lazy() {
                const { Component } = await import('@/app/routes/agent')
                return {
                    Component: () => (
                        <ProtectedRoute>
                            <Component />
                        </ProtectedRoute>
                    )
                }
            }
        },
                {
                    path: '*',
                    lazy: () => import('@/app/routes/not-found')
                }
            ]
        }
    ])

export default function AppRouter() {
    return <RouterProvider router={createAppRouter()} />
}
