import AgentSetting from '@/components/agent-setting'
import ButtonIcon from '@/components/button-icon'
import ModelTag from '@/components/model-tag'
import QuestionInput from '@/components/question-input'
import RightSidebar from '@/components/right-sidebar'
import Sidebar from '@/components/sidebar'
import { SidebarProvider } from '@/components/ui/sidebar'
import { useAppEvents } from '@/hooks/use-app-events'
import { useQuestionHandlers } from '@/hooks/use-question-handlers'
import { useSessionManager } from '@/hooks/use-session-manager'
import { useAuth } from '@/contexts/auth-context'
import {
    selectCurrentQuestion,
    setCurrentQuestion,
    useAppDispatch,
    useAppSelector
} from '@/state'
import { useState} from 'react'
import { WebSocketConnectionState } from '@/typings'

function HomePageContent() {
    const dispatch = useAppDispatch()
    const { handleEvent } = useAppEvents()
    const { user } = useAuth()
    const [isOpenSetting, setIsOpenSetting] = useState(false)

    const wsConnectionState = useAppSelector(
        (state) => state.agent.wsConnectionState
    )

    useSessionManager({
        handleEvent
    })

    const { handleEnhancePrompt, handleQuestionSubmit, handleKeyDown } =
        useQuestionHandlers()

    const currentQuestion = useAppSelector(selectCurrentQuestion)

    return (
        <>
            <div className="flex h-screen">
                <div>
                    <SidebarProvider>
                        <Sidebar />
                    </SidebarProvider>
                </div>
                <div className="flex-1 py-12 px-[126px] flex items-center justify-center">
                    <div className="w-full max-w-[768px]">
                        <p className="text-[32px] font-bold dark:text-sky-blue">
                            Hello
                            {user?.first_name ? `, ${user?.first_name}` : ''}!
                        </p>
                        <p className="text-2xl dark:text-sky-blue">
                            What can I do for you today?
                        </p>
                        <div className="flex gap-x-2 mt-6 mb-2">
                            <ModelTag />
                            <ButtonIcon
                                name="setting"
                                onClick={() => setIsOpenSetting(true)}
                            />
                        </div>
                        <QuestionInput
                            value={currentQuestion}
                            setValue={(val) => {
                                dispatch(setCurrentQuestion(val))
                            }}
                            handleKeyDown={handleKeyDown}
                            handleSubmit={(val: string) =>
                                handleQuestionSubmit(val, true)
                            }
                            isDisabled={
                                wsConnectionState !==
                                WebSocketConnectionState.CONNECTED
                            }
                            handleEnhancePrompt={handleEnhancePrompt}
                        />
                    </div>
                </div>
                <RightSidebar />
            </div>
            <AgentSetting
                isOpen={isOpenSetting}
                onOpenChange={setIsOpenSetting}
            />
        </>
    )
}

export function HomePage() {
    return <HomePageContent />
}

export const Component = HomePage
