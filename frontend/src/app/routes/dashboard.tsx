import clsx from 'clsx'
import { useMemo, useState } from 'react'
import { useNavigate } from 'react-router'

import { But<PERSON> } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import Sidebar from '@/components/sidebar'
import RightSidebar from '@/components/right-sidebar'
import { SidebarProvider } from '@/components/ui/sidebar'
import { selectSessions, useAppSelector } from '@/state'

enum TAB {
    ALL = 'All',
    RECENT = 'Recent',
    FAVORITE = 'Favorite'
}

export function DashboardPage() {
    const navigate = useNavigate()
    const [activeTab, setActiveTab] = useState(TAB.ALL)
    const [favoriteList, setFavoriteList] = useState<string[]>([])

    const sessions = useAppSelector(selectSessions)

    const handleBack = () => {
        navigate(-1)
    }

    const toggleFavorite = (id: string) => {
        if (favoriteList.includes(id)) {
            setFavoriteList(favoriteList.filter((id) => id !== id))
        } else {
            setFavoriteList([...favoriteList, id])
        }
    }

    const isFavorite = (id: string) => {
        return favoriteList.includes(id)
    }

    const sessionsByTab = useMemo(() => {
        const filteredSessions = sessions?.filter((session) => {
            return session.name
        })
        switch (activeTab) {
            case TAB.ALL:
                return filteredSessions
            case TAB.RECENT:
                return filteredSessions?.slice(0, 10)
            case TAB.FAVORITE:
                return filteredSessions?.filter((session) => {
                    return isFavorite(session.id)
                })
            default:
                return filteredSessions
        }
    }, [activeTab, sessions, favoriteList])

    return (
        <div className="flex h-screen">
            <div>
                <SidebarProvider>
                    <Sidebar />
                </SidebarProvider>
            </div>
            <div className="flex justify-center pt-[96px] flex-1">
                <div className="flex flex-col gap-y-10 w-full max-w-[768px]">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-x-4">
                            <button
                                className="cursor-pointer"
                                onClick={handleBack}
                            >
                                <Icon
                                    name="arrow-left"
                                    className="size-8 hidden dark:inline"
                                />
                                <Icon
                                    name="arrow-left-dark"
                                    className="size-8 inline dark:hidden"
                                />
                            </button>
                            <span className="text-black dark:text-sky-blue text-[32px] font-bold">
                                Dashboard
                            </span>
                        </div>
                        <div className="flex items-center gap-x-2">
                            <Button
                                className={clsx(
                                    'h-7 text-xs font-bold px-4 rounded-full border border-firefly dark:border-sky-blue',
                                    {
                                        'bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                            activeTab === TAB.ALL,
                                        'text-firefly dark:text-sky-blue':
                                            activeTab !== TAB.ALL
                                    }
                                )}
                                onClick={() => setActiveTab(TAB.ALL)}
                            >
                                All
                            </Button>
                            <Button
                                className={clsx(
                                    'h-7 text-xs font-bold px-4 rounded-full border border-firefly dark:border-sky-blue',
                                    {
                                        'bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                            activeTab === TAB.RECENT,
                                        'text-firefly dark:text-sky-blue':
                                            activeTab !== TAB.RECENT
                                    }
                                )}
                                onClick={() => setActiveTab(TAB.RECENT)}
                            >
                                Recent
                            </Button>
                            <Button
                                className={clsx(
                                    'h-7 text-xs font-bold px-4 rounded-full border border-firefly dark:border-sky-blue',
                                    {
                                        'bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                            activeTab === TAB.FAVORITE,
                                        'text-firefly dark:text-sky-blue':
                                            activeTab !== TAB.FAVORITE
                                    }
                                )}
                                onClick={() => setActiveTab(TAB.FAVORITE)}
                            >
                                Favorite
                            </Button>
                        </div>
                    </div>
                    <div className="flex-1 divide-y divide-black/30 dark:divide-white/30 overflow-auto pb-8">
                        {sessionsByTab?.map((session) => (
                            <div
                                key={session.id}
                                className="flex items-center justify-between px-4 py-3"
                            >
                                <div className="flex items-center gap-4">
                                    <Icon
                                        name="folder-3"
                                        className="size-10 fill-black dark:fill-white"
                                    />
                                    <div className="flex flex-col gap-1 text-sm">
                                        <p className="font-bold">
                                            {session.name}
                                        </p>
                                        <p>{`Message here`}</p>
                                    </div>
                                </div>
                                <div className="flex items-center gap-4">
                                    <Button
                                        size="icon"
                                        className="w-auto"
                                        onClick={() =>
                                            toggleFavorite(session.id)
                                        }
                                    >
                                        {isFavorite(session.id) ? (
                                            <Icon
                                                name="star-fill"
                                                className="fill-yellow dark:fill-white size-[18px]"
                                            />
                                        ) : (
                                            <Icon
                                                name="star"
                                                className="stroke-black dark:stroke-white size-[18px]"
                                            />
                                        )}
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <RightSidebar />
        </div>
    )
}

export const Component = DashboardPage
