import './global.css'

import {
    selectAvailableModels,
    selectSelectedModel,
    setAvailableModels,
    setSelectedModel,
    store,
    useAppDispatch
} from '@/state'
import { useCallback, useEffect } from 'react'

import AppProvider from '@/app/provider'
import AppRouter from '@/app/router'
import { Toaster } from '@/components/ui/sonner'
import { settingsService } from '@/services/settings.service'

export default function App() {
    const dispatch = useAppDispatch()

    const fetchAvailableModels = useCallback(async () => {
        try {
            const data = await settingsService.getAvailableModels()
            dispatch(setAvailableModels(data?.models || []))

            if (data?.models?.length > 0) {
                const firstModel = data?.models[0]

                // Get current state values directly from store
                const state = store.getState()
                const currentSelectedModel = selectSelectedModel(state)
                const currentAvailableModels = selectAvailableModels(state)

                if (
                    !currentSelectedModel ||
                    (currentSelectedModel &&
                        !currentAvailableModels.find(
                            (model) => model.id === currentSelectedModel
                        ))
                ) {
                    dispatch(setSelectedModel(firstModel.id))
                }
            }
        } catch {
            console.log('Failed to fetch llm models')
        }
    }, [dispatch])

    useEffect(() => {
        fetchAvailableModels()
    }, [fetchAvailableModels])

    return (
        <AppProvider>
            <AppRouter />
            <Toaster richColors />
        </AppProvider>
    )
}
