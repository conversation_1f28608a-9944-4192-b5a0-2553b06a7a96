import { useEffect, useRef } from 'react'
import { useLocation, useParams } from 'react-router'
import { useSocketIOContext } from '@/contexts/websocket-context'

export function useNavigationLeaveSession() {
    const location = useLocation()
    const { sessionId } = useParams()
    const { socket } = useSocketIOContext()
    const previousSessionIdRef = useRef<string | undefined>(sessionId)
    const previousPathRef = useRef<string>(location.pathname)

    useEffect(() => {
        const currentPath = location.pathname
        const previousPath = previousPathRef.current
        const previousSessionId = previousSessionIdRef.current

        const isLeavingAgentPage = 
            previousSessionId && 
            !currentPath.includes(previousSessionId) &&
            previousPath.includes(previousSessionId)

        if (isLeavingAgentPage && socket?.connected) {
            console.log('Leaving agent page, emitting leave_session for:', previousSessionId)
            socket.emit('leave_session', {
                session_uuid: previousSessionId
            })
        }

        previousPathRef.current = currentPath
        previousSessionIdRef.current = sessionId
    }, [location.pathname, sessionId, socket])
}