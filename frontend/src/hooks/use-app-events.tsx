'use client'

// import chalk from 'chalk'
import { cloneDeep, debounce } from 'lodash'
import { RefObject, useCallback, useEffect, useRef } from 'react'
import { toast } from 'sonner'
import { Terminal as XTerm } from '@xterm/xterm'
import { useTerminal } from '@/contexts/terminal-context'
import { useNavigate } from 'react-router'

import { setIsUploading, setRequireClearFiles } from '@/state/slice/files'
import {
    addMessage,
    selectMessages,
    setMessages,
    updateMessage
} from '@/state/slice/messages'
import {
    setGeneratingPrompt,
    setIsCreatingSession,
    setIsFromNewQuestion,
    setLoading
} from '@/state/slice/ui'
import {
    selectWorkspaceInfo,
    setBrowserUrl,
    setCurrentQuestion,
    setVscodeUrl,
    setWorkspaceInfo
} from '@/state/slice/workspace'
import { useAppDispatch, useAppSelector } from '@/state/store'
import {
    ActionStep,
    AgentEvent,
    BUILD_STEP,
    Message,
    TOOL
} from '@/typings/agent'
import {
    setActiveFile,
    setAgentInitialized,
    setBuildStep,
    setCompleted,
    setCurrentActionData,
    setPlans,
    setSelectedBuildStep
} from '@/state'
import { setResultUrl, setStopped } from '@/state/slice/agent'
import { setActiveSessionId } from '@/state/slice/sessions'
import { extractUrls } from '@/lib/utils'

export function useAppEvents({
    xtermRef
}: {
    xtermRef?: RefObject<XTerm | null>
} = {}) {
    const navigate = useNavigate()
    const { xtermRef: globalXtermRef } = useTerminal()
    // Use the provided ref or fall back to the global context ref
    const activeXtermRef = xtermRef || globalXtermRef

    const dispatch = useAppDispatch()
    const messages = useAppSelector(selectMessages)
    const workspaceInfo = useAppSelector(selectWorkspaceInfo)
    const messagesRef = useRef(messages)
    const workspaceInfoRef = useRef(workspaceInfo)
    const terminalUsernameRef = useRef<string>('')

    useEffect(() => {
        messagesRef.current = messages
    }, [JSON.stringify(messages)])

    useEffect(() => {
        workspaceInfoRef.current = workspaceInfo
    }, [workspaceInfo])

    // Create a custom dispatch function that updates messagesRef immediately
    const safeDispatch = useCallback(
        (
            action:
                | ReturnType<typeof addMessage>
                | ReturnType<typeof updateMessage>
                | ReturnType<typeof setMessages>
                | ReturnType<typeof setWorkspaceInfo>
                | { type: string; payload: unknown }
        ) => {
            // Handle different action types and update messagesRef immediately
            if (action.type === addMessage.type) {
                messagesRef.current = [
                    ...messagesRef.current,
                    action.payload as Message
                ]
            } else if (action.type === updateMessage.type) {
                messagesRef.current = messagesRef.current.map((msg) =>
                    msg.id === (action.payload as Message).id
                        ? (action.payload as Message)
                        : msg
                )
            } else if (action.type === setMessages.type) {
                messagesRef.current = action.payload as Message[]
            } else if (action.type === setWorkspaceInfo.type) {
                workspaceInfoRef.current = action.payload as string
            }

            // Call the actual dispatch
            dispatch(action)
        },
        [dispatch]
    )

    const handleEvent = useCallback(
        (
            data: {
                id: string
                type: AgentEvent
                content: Record<string, unknown>
            },
            ignoreClickAction?: boolean
        ) => {
            switch (data.type) {
                case AgentEvent.AGENT_INITIALIZED: {
                    dispatch(setAgentInitialized(true))
                    dispatch(setVscodeUrl(data.content.vscode_url as string))
                    break
                }

                case AgentEvent.AGENT_RESPONSE_INTERRUPTED: {
                    dispatch(setLoading(false))
                    dispatch(setStopped(true))
                    dispatch(setLoading(false))

                    break
                }

                case AgentEvent.SYSTEM: {
                    if (data.content.type === 'reviewer_agent') {
                        safeDispatch(
                            addMessage({
                                id: data.id,
                                role: 'assistant',
                                action: {
                                    type: TOOL.REVIEWER_AGENT,
                                    data: {
                                        content: data.content.message as string
                                    }
                                },
                                timestamp: Date.now()
                            })
                        )
                    } else if (data.content.session_id) {
                        dispatch(
                            setActiveSessionId(
                                data.content.session_id as string
                            )
                        )
                        dispatch(setIsFromNewQuestion(true))
                        dispatch(setIsCreatingSession(false))
                        setTimeout(() => {
                            dispatch(setCurrentQuestion(''))
                            dispatch(setRequireClearFiles(true))
                            navigate(`/${data.content.session_id}`)
                        }, 0)
                    } else {
                        safeDispatch(
                            addMessage({
                                id: data.id,
                                role: 'assistant',
                                content: data.content.message as string,
                                timestamp: Date.now()
                            })
                        )
                    }
                    break
                }

                case AgentEvent.USER_MESSAGE: {
                    const messageContent = data.content.text as string
                    const currentMessages = messagesRef.current
                    const isDuplicate = currentMessages.some(
                        (msg) =>
                            msg.role === 'user' &&
                            msg.content === messageContent
                    )

                    if (!isDuplicate) {
                        safeDispatch(
                            addMessage({
                                id: data.id,
                                role: 'user',
                                content: messageContent,
                                timestamp: Date.now()
                            })
                        )
                    }
                    dispatch(setCompleted(false))
                    break
                }

                case AgentEvent.PROMPT_GENERATED: {
                    dispatch(setGeneratingPrompt(false))
                    dispatch(setCurrentQuestion(data.content.result as string))
                    break
                }

                case AgentEvent.PROCESSING: {
                    dispatch(setLoading(true))
                    break
                }

                case AgentEvent.AGENT_THINKING: {
                    safeDispatch(
                        addMessage({
                            id: data.id,
                            role: 'assistant',
                            content: data.content.text as string,
                            timestamp: Date.now(),
                            isThinkMessage: true
                        })
                    )
                    break
                }

                case AgentEvent.TOOL_CALL: {
                    if (data.content.tool_name === TOOL.SEQUENTIAL_THINKING) {
                        safeDispatch(
                            addMessage({
                                id: data.id,
                                role: 'assistant',
                                content: (
                                    data.content.tool_input as {
                                        thought: string
                                    }
                                ).thought as string,
                                timestamp: Date.now()
                            })
                        )
                    } else if (data.content.tool_name === TOOL.MESSAGE_USER) {
                        safeDispatch(
                            addMessage({
                                id: data.id,
                                role: 'assistant',
                                content: (
                                    data.content.tool_input as { text: string }
                                ).text as string,
                                timestamp: Date.now()
                            })
                        )
                    } else {
                        const message: Message = {
                            id: data.id,
                            role: 'assistant',
                            action: {
                                type: data.content.tool_name as TOOL,
                                data: data.content
                            },
                            timestamp: Date.now()
                        }
                        const url = (data.content.tool_input as { url: string })
                            ?.url as string
                        if (url) {
                            dispatch(setBrowserUrl(url))
                        }
                        const command = (
                            data.content.tool_input as { command: string }
                        ).command as string
                        if (command) {
                            activeXtermRef.current?.writeln(command)
                        }
                        safeDispatch(addMessage(message))
                        if (!ignoreClickAction) {
                            handleClickAction(message.action)
                        }
                    }
                    break
                }

                case AgentEvent.BROWSER_USE:
                    // Commented out in original code
                    break

                case AgentEvent.TOOL_RESULT: {
                    if (data.content.tool_name === TOOL.BROWSER_USE) {
                        safeDispatch(
                            addMessage({
                                id: data.id,
                                role: 'assistant',
                                content: data.content.result as string,
                                timestamp: Date.now()
                            })
                        )
                    } else {
                        if (
                            data.content.tool_name !==
                                TOOL.SEQUENTIAL_THINKING &&
                            data.content.tool_name !== TOOL.PRESENTATION &&
                            data.content.tool_name !== TOOL.MESSAGE_USER &&
                            data.content.tool_name !==
                                TOOL.RETURN_CONTROL_TO_USER
                        ) {
                            // Get the latest messages from our ref
                            const messages = [...messagesRef.current]

                            // Find the last message with a matching tool call
                            let lastToolCallMessageIndex = -1
                            for (let i = messages.length - 1; i >= 0; i--) {
                                if (
                                    messages[i].action?.type ===
                                        data.content.tool_name &&
                                    !messages[i].action?.data?.isResult
                                ) {
                                    lastToolCallMessageIndex = i
                                    break
                                }
                            }

                            // If we found a matching tool call message
                            if (lastToolCallMessageIndex !== -1) {
                                const lastToolCallMessage = cloneDeep(
                                    messages[lastToolCallMessageIndex]
                                )

                                if (lastToolCallMessage?.action) {
                                    lastToolCallMessage.action.data.result = `${data.content.result}`
                                    if (
                                        [
                                            TOOL.BROWSER_VIEW,
                                            TOOL.BROWSER_CLICK,
                                            TOOL.BROWSER_ENTER_TEXT,
                                            TOOL.BROWSER_PRESS_KEY,
                                            TOOL.BROWSER_GET_SELECT_OPTIONS,
                                            TOOL.BROWSER_SELECT_DROPDOWN_OPTION,
                                            TOOL.BROWSER_SWITCH_TAB,
                                            TOOL.BROWSER_OPEN_NEW_TAB,
                                            TOOL.BROWSER_WAIT,
                                            TOOL.BROWSER_SCROLL_DOWN,
                                            TOOL.BROWSER_SCROLL_UP,
                                            TOOL.BROWSER_NAVIGATION,
                                            TOOL.BROWSER_RESTART
                                        ].includes(
                                            data.content.tool_name as TOOL
                                        )
                                    ) {
                                        lastToolCallMessage.action.data.result =
                                            data.content.result &&
                                            Array.isArray(data.content.result)
                                                ? data.content.result.find(
                                                      (item) =>
                                                          item.type === 'image'
                                                  )?.source?.data
                                                : undefined
                                    }
                                    lastToolCallMessage.action.data.isResult =
                                        true
                                    if (!ignoreClickAction) {
                                        setTimeout(() => {
                                            handleClickAction(
                                                lastToolCallMessage.action
                                            )
                                        }, 50)
                                    }

                                    safeDispatch(
                                        updateMessage(lastToolCallMessage)
                                    )
                                }
                            } else {
                                // If no matching tool call message was found, fall back to using the last message
                                const lastMessage = cloneDeep(
                                    messages[messages.length - 1]
                                )
                                safeDispatch(
                                    addMessage({
                                        ...lastMessage,
                                        action: data.content as ActionStep
                                    })
                                )
                            }
                        }
                    }
                    break
                }

                case AgentEvent.AGENT_RESPONSE: {
                    const text = data.content.text as string

                    safeDispatch(
                        addMessage({
                            id: data.id,
                            role: 'assistant',
                            content: text,
                            timestamp: Date.now()
                        })
                    )
                    break
                }

                case AgentEvent.COMPLETE: {
                    dispatch(setCompleted(true))
                    dispatch(setLoading(false))
                    setTimeout(() => {
                        dispatch(setBuildStep(BUILD_STEP.RESULT))
                    }, 50)
                    break
                }

                case AgentEvent.UPLOAD_SUCCESS: {
                    safeDispatch(setIsUploading(false))

                    // Update the uploaded files state
                    const newFiles = data.content.files as {
                        path: string
                        saved_path: string
                    }[]

                    // Filter out files that are part of folders
                    const folderMetadataFiles = newFiles.filter((f) =>
                        f.path.startsWith('folder:')
                    )

                    const folderNames = folderMetadataFiles
                        .map((f) => {
                            const match = f.path.match(/^folder:(.+):\d+$/)
                            return match ? match[1] : null
                        })
                        .filter(Boolean) as string[]

                    // Only add files that are not part of folders or are folder metadata files
                    const filesToAdd = newFiles.filter((f) => {
                        // If it's a folder metadata file, include it
                        if (f.path.startsWith('folder:')) {
                            return true
                        }

                        // For regular files, exclude them if they might be part of a folder
                        return !folderNames.some((folderName) =>
                            f.path.includes(folderName)
                        )
                    })

                    const paths = filesToAdd.map((f) => f.path)
                    safeDispatch({ type: 'ADD_UPLOADED_FILES', payload: paths })
                    break
                }

                case 'error': {
                    toast.error(data.content.message as string)
                    safeDispatch(setIsUploading(false))
                    safeDispatch(setLoading(false))
                    safeDispatch(setGeneratingPrompt(false))
                    break
                }
            }
        },
        [safeDispatch]
    )

    const handleClickAction = useCallback(
        debounce((data: ActionStep | undefined, showTabOnly = false) => {
            if (!data) return

            switch (data.type) {
                case TOOL.WEB_SEARCH:
                    dispatch(setCurrentActionData(data))
                    if (showTabOnly) {
                        dispatch(setSelectedBuildStep(BUILD_STEP.BUILD))
                    } else {
                        dispatch(setBuildStep(BUILD_STEP.BUILD))
                    }
                    break

                case TOOL.IMAGE_GENERATE:
                case TOOL.VIDEO_GENERATE:
                case TOOL.IMAGE_SEARCH:
                case TOOL.BROWSER_USE:
                case TOOL.VISIT:
                    dispatch(setCurrentActionData(data))
                    if (showTabOnly) {
                        dispatch(setSelectedBuildStep(BUILD_STEP.BUILD))
                    } else {
                        dispatch(setBuildStep(BUILD_STEP.BUILD))
                    }
                    if (
                        data.type === TOOL.IMAGE_GENERATE ||
                        data.type === TOOL.VIDEO_GENERATE
                    ) {
                        dispatch(setResultUrl(data.data.result as string))
                    }
                    break

                case TOOL.BROWSER_CLICK:
                case TOOL.BROWSER_ENTER_TEXT:
                case TOOL.BROWSER_PRESS_KEY:
                case TOOL.BROWSER_GET_SELECT_OPTIONS:
                case TOOL.BROWSER_SELECT_DROPDOWN_OPTION:
                case TOOL.BROWSER_SWITCH_TAB:
                case TOOL.BROWSER_OPEN_NEW_TAB:
                case TOOL.BROWSER_VIEW:
                case TOOL.BROWSER_NAVIGATION:
                case TOOL.BROWSER_RESTART:
                case TOOL.BROWSER_WAIT:
                case TOOL.BROWSER_SCROLL_DOWN:
                case TOOL.BROWSER_SCROLL_UP:
                    dispatch(setCurrentActionData(data))
                    if (showTabOnly) {
                        dispatch(setSelectedBuildStep(BUILD_STEP.BUILD))
                    } else {
                        dispatch(setBuildStep(BUILD_STEP.BUILD))
                    }
                    break

                case TOOL.LS:
                    dispatch(setCurrentActionData(data))
                    if (showTabOnly) {
                        dispatch(setSelectedBuildStep(BUILD_STEP.BUILD))
                    } else {
                        dispatch(setBuildStep(BUILD_STEP.BUILD))
                    }
                    if (!showTabOnly) {
                        setTimeout(() => {
                            if (data.data.result && activeXtermRef?.current) {
                                activeXtermRef.current?.writeln('ls')
                                const lines = `${data.data.result || ''}`.split(
                                    '\n'
                                )
                                for (
                                    let index = 0;
                                    index < lines.length;
                                    index++
                                ) {
                                    const line = lines[index]
                                    activeXtermRef.current.writeln(line)
                                }
                                activeXtermRef.current.write('$ ')
                            }
                        }, 500)
                    }
                    break

                case TOOL.BASH:
                case TOOL.BASH_INIT:
                case TOOL.BASH_VIEW:
                case TOOL.BASH_STOP:
                case TOOL.BASH_KILL:
                    if (showTabOnly) {
                        dispatch(setSelectedBuildStep(BUILD_STEP.BUILD))
                    } else {
                        dispatch(setBuildStep(BUILD_STEP.BUILD))
                    }
                    dispatch(setCurrentActionData(data))
                    if (!showTabOnly && data.type === TOOL.BASH) {
                        setTimeout(() => {
                            if (data.data.result && activeXtermRef?.current) {
                                const lines = `${data.data.result || ''}`.split(
                                    '\n'
                                )
                                for (
                                    let index = 0;
                                    index < lines.length;
                                    index++
                                ) {
                                    const line = lines[index]
                                    let username = terminalUsernameRef.current
                                        ? `${terminalUsernameRef.current}`
                                        : ''
                                    if (!username) {
                                        username = `${line}`.split(' ')?.[0]
                                    }
                                    // const formatLine = line?.replace(
                                    //     username,
                                    //     `${chalk.hex('#00a63e')(username)}`
                                    // )
                                    const formatLine = line
                                    if (index === 0) {
                                        activeXtermRef.current.writeln(
                                            formatLine?.replace(
                                                terminalUsernameRef.current,
                                                ''
                                            )
                                        )
                                    } else if (index === lines.length - 1) {
                                        terminalUsernameRef.current =
                                            line.trim()
                                        activeXtermRef.current.write(
                                            `${formatLine} `
                                        )
                                    } else {
                                        activeXtermRef.current.writeln(
                                            formatLine
                                        )
                                    }
                                    terminalUsernameRef.current = username
                                }
                                activeXtermRef.current.write('$ ')
                            } else if (
                                data.data.result &&
                                !activeXtermRef?.current
                            ) {
                                console.warn(
                                    'Terminal not initialized yet, skipping output:',
                                    data.data.result
                                )
                            }
                        }, 500)
                    }
                    break

                case TOOL.READ:
                case TOOL.WRITE:
                case TOOL.EDIT: {
                    if (showTabOnly) {
                        dispatch(setSelectedBuildStep(BUILD_STEP.BUILD))
                    } else {
                        dispatch(setBuildStep(BUILD_STEP.BUILD))
                    }
                    const path =
                        data.data.tool_input?.file_path ||
                        data.data.tool_input?.file
                    if (path) {
                        dispatch(setActiveFile(path))
                    }
                    setTimeout(() => {
                        dispatch(setCurrentActionData(data))
                    }, 50)
                    break
                }

                case TOOL.TODO_WRITE:
                    dispatch(setPlans(data.data.tool_input?.todos || []))
                    dispatch(setBuildStep(BUILD_STEP.PLAN))
                    break

                case TOOL.REGISTER_DEPLOYMENT: {
                    const urls = extractUrls(data.data?.result as string)

                    for (const url of urls) {
                        if (url) {
                            dispatch(setResultUrl(url))
                            break
                        }
                    }
                    break
                }

                default:
                    break
            }
        }, 50),
        [safeDispatch]
    )

    return { handleEvent, handleClickAction }
}
