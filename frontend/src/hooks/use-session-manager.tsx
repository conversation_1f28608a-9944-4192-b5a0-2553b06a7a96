import { useState, useEffect, useCallback, useRef } from 'react'
import { useParams } from 'react-router'
import { AgentEvent, Message, IEvent } from '@/typings/agent'
import {
    selectIsFromNewQuestion,
    setAgentInitialized,
    setCompleted,
    setIsFromNewQuestion,
    setLoading,
    setMessages,
    useAppDispatch,
    useAppSelector
} from '@/state'
import { sessionService } from '@/services/session.service'

export function useSessionManager({
    handleEvent
}: {
    handleEvent: (
        data: {
            id: string
            type: AgentEvent
            content: Record<string, unknown>
        },
        ignoreClickAction?: boolean
    ) => void
}) {
    const dispatch = useAppDispatch()
    const params = useParams()
    const isFromNewQuestion = useAppSelector(selectIsFromNewQuestion)
    const [sessionId, setSessionId] = useState<string | null>(null)
    const [isLoadingSession, setIsLoadingSession] = useState(false)
    const [isReplayMode, setIsReplayMode] = useState(false)
    const eventsDataRef = useRef<{
        events: IEvent[]
    } | null>(null)
    const delayTimeRef = useRef<number>(1500)
    const fetchingRef = useRef<boolean>(false)

    // Get session ID from URL params and determine replay mode
    useEffect(() => {
        const id = params.sessionId || null
        setSessionId(id)
        dispatch(setAgentInitialized(false))

        // If navigated from new question submission, it's not replay mode
        if (isFromNewQuestion) {
            setIsReplayMode(false)
        } else if (id) {
            // Otherwise, if there's a session ID in the URL, it's replay mode
            setIsReplayMode(true)
        } else {
            setIsReplayMode(false)
        }
    }, [params.sessionId, isFromNewQuestion, dispatch])

    const processAllEventsImmediately = () => {
        delayTimeRef.current = 0
    }

    const fetchSessionEvents = useCallback(async () => {
        const id = params.sessionId
        if (!id || fetchingRef.current) return

        fetchingRef.current = true
        setIsLoadingSession(true)
        try {
            const data = await sessionService.getSessionEvents(id)

            if (data.events && Array.isArray(data.events)) {
                // Store events data for potential immediate processing
                eventsDataRef.current = { events: data.events }

                // Process events to reconstruct the conversation
                const reconstructedMessages: Message[] = []

                // Function to process events with delay
                const processEventsWithDelay = async () => {
                    dispatch(setLoading(true))
                    for (let i = 0; i < data.events.length; i++) {
                        const event = data.events[i]

                        // Skip AGENT_INITIALIZED events when replaying session history
                        // We want to force re-initialization when accessing existing sessions
                        if (event.type !== AgentEvent.AGENT_INITIALIZED) {
                            handleEvent(
                                {
                                    id: event.id,
                                    type: event.type,
                                    content: event.content.content
                                },
                                true
                            )
                        } else {
                            console.log(
                                'Skipping AGENT_INITIALIZED event during session replay'
                            )
                        }
                    }
                    dispatch(setLoading(false))
                }

                // Start processing events with delay
                processEventsWithDelay()

                // Set the reconstructed messages
                if (reconstructedMessages.length > 0) {
                    dispatch(setMessages(reconstructedMessages))
                    dispatch(setCompleted(true))
                }
            }
        } catch (error) {
            console.error('Failed to fetch session events:', error)
        } finally {
            setIsLoadingSession(false)
            fetchingRef.current = false
        }
    }, [params.sessionId, isReplayMode, handleEvent, dispatch])

    useEffect(() => {
        fetchSessionEvents()
    }, [fetchSessionEvents])

    const setSessionIdWithSource = useCallback(
        (id: string, fromNewQuestion = false) => {
            if (fromNewQuestion) {
                dispatch(setIsFromNewQuestion(true))
            }
            setSessionId(id)
        },
        [dispatch]
    )

    return {
        sessionId,
        isLoadingSession,
        isReplayMode,
        setSessionId: setSessionIdWithSource,
        fetchSessionEvents,
        processAllEventsImmediately
    }
}
