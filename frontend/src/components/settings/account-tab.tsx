import { useAppSelector } from '@/state/store'
import { selectUser } from '@/state/slice/user'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { getFirstCharacters } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/auth-context'
import { useNavigate } from 'react-router'

const AccountTab = () => {
    const user = useAppSelector(selectUser)
    const { logout } = useAuth()
    const navigate = useNavigate()

    const handleLogout = () => {
        logout()
        navigate('/login')
    }

    return (
        <div className="space-y-6 pt-2">
            <div className="flex items-center gap-4 mb-6">
                <Avatar className="size-14">
                    <AvatarImage src={user?.avatar} />
                    <AvatarFallback>
                        {user?.first_name
                            ? getFirstCharacters(
                                  `${user?.first_name} ${user?.last_name}`
                              )
                            : `II`}
                    </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                    <p className="text-[18px] font-bold">{`${user?.first_name} ${user?.last_name}`}</p>
                    <p className="text-sm">{user?.email}</p>
                </div>
            </div>

            <div className="space-y-4">
                <h2 className="text-[18px] font-bold mb-4">Linked Accounts</h2>
                <div className="flex items-center justify-between">
                    <p className="text-sm">Google</p>
                    <Button className="text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
                <div className="flex items-center justify-between">
                    <p className="text-sm">Microsoft</p>
                    <Button className="text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
                <div className="flex items-center justify-between">
                    <p className="text-sm">GitHub</p>
                    <Button className="text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
                <div className="flex items-center justify-between">
                    <p className="text-sm">Apple</p>
                    <Button className="text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
            </div>

            <div className="py-6 border-t border-white/30">
                <h2 className="text-[18px] font-bold mb-4">Security</h2>
                <div className="flex items-center gap-x-4">
                    <Button
                        size="xl"
                        className="bg-sky-blue text-black font-bold w-[247px]"
                        onClick={handleLogout}
                    >
                        Log out
                    </Button>

                    <Button
                        size="xl"
                        variant="outline"
                        className="text-red border-red w-[247px]"
                    >
                        Delete account
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default AccountTab
