import React from 'react'

interface SearchBrowserProps {
    className?: string
    keyword?: string
    search_results?: string | Record<string, unknown> | undefined
}

const SearchBrowser = React.memo(
    ({ className, keyword, search_results }: SearchBrowserProps) => {
        if (!keyword) return

        return (
            <div
                className={`flex-1 bg-white dark:bg-charcoal px-6 divide-y divide-grey-2/30 dark:divide-white/30 overflow-auto ${className}`}
            >
                {Array.isArray(search_results) &&
                    search_results?.map((item, index) => (
                        <div key={index} className="flex flex-col gap-y-2 py-6">
                            <a
                                href={item.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="font-semibold text-firefly dark:text-sky-blue-2 hover:underline text-lg"
                            >
                                {item.title}
                            </a>
                            <p className="text-neutral-400 text-sm line-clamp-2">
                                {item.content}
                            </p>
                            <span className="text-green-400 text-xs line-clamp-1 break-all">
                                {item.url}
                            </span>
                        </div>
                    ))}
            </div>
        )
    }
)

SearchBrowser.displayName = 'SearchBrowser'

export default SearchBrowser
