import { useMemo } from 'react'
import last from 'lodash/last'

import {
    selectCurrentActionData,
    selectFilesContent,
    useAppSelector
} from '@/state'
import { Icon } from '../ui/icon'
import CodeEditor from '../code-editor'
import { TOOL } from '@/typings/agent'
import Terminal from '../terminal'
import { parseJson } from '@/lib/utils'
import Browser from './browser'
import DiffCodeEditor from '../diff-editor'
import SearchBrowser from './search-browser'

interface AgentBuildProps {
    className?: string
}

const AgentBuild = ({ className }: AgentBuildProps) => {
    const currentActionData = useAppSelector(selectCurrentActionData)
    const fileContents = useAppSelector(selectFilesContent)

    const tab = useMemo(() => {
        // browser
        if (
            currentActionData?.type &&
            [TOOL.VISIT, TOOL.IMAGE_GENERATE, TOOL.VIDEO_GENERATE].includes(
                currentActionData?.type
            )
        )
            return 'browser'

        // image_browser
        if (
            currentActionData?.type &&
            [TOOL.IMAGE_SEARCH].includes(currentActionData?.type)
        )
            return 'image_browser'

        // search_browser
        if (
            currentActionData?.type &&
            [TOOL.WEB_SEARCH].includes(currentActionData?.type)
        )
            return 'search_browser'

        // terminal
        if (
            currentActionData?.type &&
            [
                TOOL.BASH,
                TOOL.BASH_INIT,
                TOOL.BASH_KILL,
                TOOL.BASH_VIEW,
                TOOL.BASH_STOP,
                TOOL.LS
            ].includes(currentActionData?.type)
        )
            return 'terminal'

        // code
        return 'code'
    }, [currentActionData?.type])

    const fileName = useMemo(() => {
        const path =
            currentActionData?.data?.tool_input?.path ||
            currentActionData?.data?.tool_input?.file_path

        if (!path) return undefined

        return path
    }, [
        currentActionData?.data?.tool_input?.path,
        currentActionData?.data?.tool_input?.file_path
    ])

    const fileContent = useMemo(() => {
        if (currentActionData?.data?.tool_name === TOOL.WRITE) {
            return currentActionData?.data?.tool_input?.content as string
        } else if (currentActionData?.data?.tool_name === TOOL.EDIT) {
            return currentActionData?.data?.tool_input?.new_string as string
        }
        return currentActionData?.data?.result as string
    }, [
        currentActionData?.data?.tool_input?.content,
        currentActionData?.data?.result,
        currentActionData?.data?.tool_input?.new_string
    ])

    const searchImages = useMemo(() => {
        if (currentActionData?.type !== TOOL.IMAGE_SEARCH) return []
        return parseJson(currentActionData?.data?.result as string) as {
            image_url: string
        }[]
    }, [currentActionData?.type, currentActionData?.data?.result])

    const buildingTitle = useMemo(() => {
        if (currentActionData?.type === TOOL.IMAGE_SEARCH) return 'Searching'
        if (currentActionData?.type === TOOL.IMAGE_GENERATE)
            return 'Generating Image'
        if (currentActionData?.type === TOOL.VIDEO_GENERATE)
            return 'Generating Video'

        if (currentActionData?.type === TOOL.WEB_SEARCH)
            return `Searching: "${currentActionData?.data?.tool_input?.query || ''}"`

        if (currentActionData?.type === TOOL.VISIT) return 'Browsing'
        return 'Generating'
    }, [currentActionData])

    const searchBrowserProps = useMemo(
        () => ({
            className:
                tab === 'search_browser'
                    ? 'h-[325px] !bg-firefly/10 dark:!bg-sky-blue/10'
                    : 'hidden',
            keyword: currentActionData?.data.tool_input?.query,
            search_results:
                currentActionData?.type === TOOL.WEB_SEARCH &&
                currentActionData?.data?.result
                    ? parseJson(currentActionData?.data?.result as string)
                    : undefined
        }),
        [currentActionData]
    )

    return (
        <div
            className={`flex-1 flex flex-col justify-between w-full ${className}`}
        >
            <div className={`flex flex-1 flex-col justify-center items-center`}>
                <div className="p-4 w-full max-w-[580px] rounded-xl bg-[#000000]">
                    <div className={`flex flex-col w-full max-w-[548px]`}>
                        <div className="flex h-8 items-center justify-center gap-[6px] w-full bg-[#f4f4f4] dark:bg-grey rounded-t-xl px-6">
                            <Icon
                                name="loading"
                                className="animate-spin fill-black size-[18px]"
                            />
                            <span className="text-sm font-bold text-black line-clamp-1">
                                {tab === 'terminal'
                                    ? 'Executing'
                                    : `${buildingTitle} ${last(fileName?.split('/')) || ''}`}
                            </span>
                        </div>
                        <div className="w-full h-[325px] bg-white dark:bg-charcoal relative rounded-b-xl overflow-hidden">
                            <CodeEditor
                                className={`w-full h-full ${tab === 'code' && currentActionData?.data?.tool_name !== TOOL.EDIT ? '' : 'hidden'}`}
                                currentActionData={currentActionData}
                                activeFile={fileName}
                                filesContent={{
                                    ...fileContents,
                                    [fileName || '']: fileContent
                                }}
                                showEditorOnly
                            />
                            <DiffCodeEditor
                                className={`w-full h-full ${tab === 'code' && currentActionData?.data?.tool_name === TOOL.EDIT ? '' : 'hidden'}`}
                                activeFile={fileName}
                                oldContent={
                                    currentActionData?.data?.tool_input
                                        ?.old_string || ''
                                }
                                newContent={
                                    currentActionData?.data?.tool_input
                                        ?.new_string || ''
                                }
                                showEditorOnly
                            />

                            <Browser
                                isHideHeader
                                className={`!h-[325px] !overflow-auto ${tab === 'browser' ? '!rounded-none' : 'hidden'}`}
                                contentClassName={`bg-firefly/10 dark:bg-sky-blue/10 h-full ${
                                    currentActionData?.type ===
                                        TOOL.IMAGE_GENERATE ||
                                    currentActionData?.type ===
                                        TOOL.VIDEO_GENERATE
                                        ? '!p-0'
                                        : ''
                                }`}
                                markdownClassName="overflow-visible h-full"
                                url={
                                    currentActionData?.type ===
                                        TOOL.IMAGE_GENERATE ||
                                    currentActionData?.type ===
                                        TOOL.VIDEO_GENERATE
                                        ? (currentActionData?.data
                                              ?.result as string)
                                        : currentActionData?.data?.tool_input
                                              ?.url
                                }
                                isVideoUrl={
                                    currentActionData?.type ===
                                    TOOL.VIDEO_GENERATE
                                }
                                screenshot={
                                    currentActionData?.data?.result as string
                                }
                                screenshotClassName="w-full h-full object-cover object-top !rounded-none overflow-hidden"
                                raw={
                                    currentActionData?.type === TOOL.VISIT
                                        ? (currentActionData?.data
                                              ?.result as string)
                                        : undefined
                                }
                            />

                            {tab === 'image_browser' && (
                                <div className="grid grid-cols-2 gap-2 h-full overflow-auto">
                                    {Array.isArray(searchImages) &&
                                        searchImages
                                            ?.slice(0, 4)
                                            ?.map(({ image_url }, index) => (
                                                <img
                                                    key={index}
                                                    src={image_url}
                                                    alt={`Image ${index + 1}`}
                                                    className="w-full aspect-[374/254] object-cover"
                                                />
                                            ))}
                                </div>
                            )}
                            <Terminal
                                className={`max-h-[325px] !p-0 ${tab === 'terminal' ? '' : 'hidden'}`}
                            />
                            <SearchBrowser {...searchBrowserProps} />
                        </div>
                    </div>
                </div>
                <p className="text-xs dark:text-white font-bold text-center mt-4">
                    Once finished, your app screen will placed here
                </p>
            </div>
            {/* <div className="flex flex-col items-center justify-center p-6 bg-firefly/10 dark:bg-sky-blue/10 rounded-xl dark:text-white w-full max-w-[580px]">
                <p className="text-2xl font-bold">
                    I’ll take 10-25 minutes to build
                </p>
                <p className="text-2xl font-bold">as per your request.</p>
                <p className="mt-4 text-sm">
                    I build well-researched, well-designed, well-written,
                </p>
                <p className="text-sm">and functional solutions each time.</p>
            </div> */}
        </div>
    )
}

export default AgentBuild
