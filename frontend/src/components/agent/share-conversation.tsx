import { toast } from 'sonner'
import { useParams } from 'react-router'

import { Icon } from '../ui/icon'
import { Sheet, SheetClose, SheetContent, SheetHeader } from '../ui/sheet'
import { useMemo } from 'react'
import { Button } from '../ui/button'

interface ShareConversationProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}

const ShareConversation = ({ open, onOpenChange }: ShareConversationProps) => {
    const { sessionId } = useParams()

    const shareUrl = useMemo(() => {
        return `${window.location.origin}/${sessionId}`
    }, [sessionId])

    const handleCopy = () => {
        navigator.clipboard.writeText(shareUrl)
        toast.success('Copied to clipboard')
    }
    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="pt-12 w-full !max-w-[560px]">
                <SheetHeader className="px-6 pt-0 gap-1 pb-4">
                    <div className="flex items-center justify-between">
                        <p className="text-2xl font-bold">Share conversation</p>
                        <div className="flex items-center gap-x-4">
                            <SheetClose className="cursor-pointer">
                                <Icon
                                    name="close"
                                    className="fill-grey-2 dark:fill-grey"
                                />
                            </SheetClose>
                        </div>
                    </div>
                </SheetHeader>
                <div className="px-6 mt-4">
                    <div className="px-4 py-3 flex items-center justify-between gap-x-4 rounded-xl border border-grey bg-grey-3 dark:bg-sky-blue-2/10">
                        <div className="flex items-center gap-x-2 text-14 flex-1">
                            <Icon
                                name="link-2"
                                className="size-6 fill-black dark:fill-white -rotate-45"
                            />
                            <span className="line-clamp-1 flex-1">
                                {shareUrl}
                            </span>
                        </div>
                        <Button
                            className="h-[22px] bg-firefly dark:bg-sky-blue-2 text-sky-blue-2 dark:text-black gap-x-[6px] text-xs rounded-sm !font-normal"
                            onClick={handleCopy}
                        >
                            <Icon
                                name="copy"
                                className="size-4 fill-sky-blue-2 dark:fill-black"
                            />
                            Copy
                        </Button>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    )
}

export default ShareConversation
