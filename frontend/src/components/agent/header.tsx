import { useNavigate, useParams } from 'react-router'

import ButtonIcon from '@/components/button-icon'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { useState } from 'react'
import ShareConversation from './share-conversation'
import { ISession } from '@/typings'

interface AgentHeaderProps {
    sessionData?: ISession
}

const AgentHeader = ({ sessionData }: AgentHeaderProps) => {
    const navigate = useNavigate()
    const { sessionId } = useParams()
    const [isFavorite, setIsFavorite] = useState(false)
    const [isShareOpen, setIsShareOpen] = useState(false)

    const handleShare = () => {
        if (!sessionId) return
        setIsShareOpen(true)
    }

    const handleBack = () => {
        navigate('/')
    }

    const toggleFavorite = () => {
        setIsFavorite((prev) => !prev)
    }

    return (
        <div className="py-3 px-6 flex items-center gap-x-4 border-b border-neutral-200 dark:border-white/30">
            <ButtonIcon
                name="home"
                className="bg-black"
                iconClassName="fill-sky-blue-2 dark:fill-black"
                onClick={handleBack}
            />
            <div className="flex items-center gap-x-[6px]">
                <img
                    src="/images/logo-only.svg"
                    className="size-6 hidden dark:inline"
                    alt="Logo"
                />
                <img
                    src="/images/logo-charcoal.svg"
                    className="size-6 inline dark:hidden"
                    alt="Logo"
                />
                <span className="text-black dark:text-white text-sm font-bold">
                    II-Agent
                </span>
            </div>
            <div className="flex gap-x-4 items-center absolute left-1/2 -translate-x-1/2">
                <div className="flex items-center gap-x-2">
                    <div className="border dark:border-white rounded-full size-6 flex items-center justify-center">
                        <Icon
                            name="lock"
                            className="fill-black dark:fill-white"
                        />
                    </div>
                    <span className="dark:text-white font-bold text-sm">
                        {sessionData?.name}
                    </span>
                </div>
                <Button size="icon" className="w-auto" onClick={handleShare}>
                    <Icon
                        name="share"
                        className="stroke-black dark:stroke-white size-[18px]"
                    />
                </Button>
                <Button size="icon" className="w-auto" onClick={toggleFavorite}>
                    {isFavorite ? (
                        <Icon
                            name="star-fill"
                            className="fill-yellow dark:fill-white size-[18px]"
                        />
                    ) : (
                        <Icon
                            name="star"
                            className="stroke-black dark:stroke-white size-[18px]"
                        />
                    )}
                </Button>
            </div>
            <ShareConversation
                open={isShareOpen}
                onOpenChange={setIsShareOpen}
            />
        </div>
    )
}

export default AgentHeader
