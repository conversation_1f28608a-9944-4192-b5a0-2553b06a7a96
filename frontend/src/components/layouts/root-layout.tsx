import { Outlet } from 'react-router'
import { WebSocketProvider } from '@/contexts/websocket-context'
import { useAppEvents } from '@/hooks/use-app-events'
import { useNavigationLeaveSession } from '@/hooks/use-navigation-leave-session'

function RootLayoutContent() {
    useNavigationLeaveSession()
    
    return <Outlet />
}

export function RootLayout() {
    const { handleEvent } = useAppEvents()

    return (
        <WebSocketProvider handleEvent={handleEvent}>
            <RootLayoutContent />
        </WebSocketProvider>
    )
}