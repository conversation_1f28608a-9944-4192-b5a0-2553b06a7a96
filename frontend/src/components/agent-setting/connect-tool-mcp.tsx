import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'

import { Icon } from '../ui/icon'
import { Sheet, Sheet<PERSON>lose, <PERSON>et<PERSON>ontent, <PERSON>et<PERSON>eader } from '../ui/sheet'
import { Form, FormControl, FormField, FormItem, FormLabel } from '../ui/form'
import { Textarea } from '../ui/textarea'
import { Button } from '../ui/button'
import { settingsService } from '@/services/settings.service'
import { IMcpSettings } from '@/typings/settings'

const FormSchema = z.object({
    tool_config: z.string()
})

interface ConnectToolMCPProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}

const ConnectToolMCP = ({ open, onOpenChange }: ConnectToolMCPProps) => {
    const [isLoading, setIsLoading] = useState(false)
    const [mcpSettings, setMcpSettings] = useState<IMcpSettings[]>([])

    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues: {
            tool_config: ''
        }
    })

    const fetchMcpSettings = async () => {
        try {
            const response = await settingsService.getMcpSettings()
            setMcpSettings(response.settings)
            if (response.settings.length > 0) {
                form.setValue(
                    'tool_config',
                    JSON.stringify(response.settings[0].config, null, 2)
                )
            }
        } catch (error) {
            console.error('Failed to fetch MCP settings:', error)
        }
    }

    const handleDelete = async () => {
        if (mcpSettings.length > 0) {
            try {
                await settingsService.deleteMcpSettings(mcpSettings[0].id)
                toast.success('MCP tool disconnected')
                setMcpSettings([])
                form.reset()
                onOpenChange(false)
            } catch (error) {
                toast.error('Failed to delete MCP tool')
                console.error('Error deleting MCP tool:', error)
            }
        }
    }

    const onSubmit = async (data: z.infer<typeof FormSchema>) => {
        if (!data.tool_config) {
            toast.error('Please enter MCP configuration')
            return
        }

        try {
            setIsLoading(true)
            const config = JSON.parse(data.tool_config)

            const mcpSetting: IMcpSettings = {
                id: Date.now().toString(),
                name: config.name || 'MCP Tool',
                config: config,
                enabled: true
            }

            if (mcpSettings.length > 0) {
                // Update existing
                await settingsService.updateMcpSettings(
                    mcpSettings[0].id,
                    mcpSetting
                )
                toast.success('MCP settings updated successfully')
            } else {
                // Create new
                await settingsService.createMcpSettings(mcpSetting)
                toast.success('MCP tool connected successfully')
            }

            onOpenChange(false)
            form.reset()
        } catch (error) {
            if (error instanceof SyntaxError) {
                toast.error('Invalid JSON format')
            } else {
                toast.error('Failed to save MCP settings')
                console.error('Error saving MCP settings:', error)
            }
        } finally {
            setIsLoading(false)
        }
    }

    useEffect(() => {
        if (open) {
            fetchMcpSettings()
        }
    }, [open])

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="px-6 py-12 w-full !max-w-[560px]">
                <SheetHeader className="p-0 gap-6 pb-4">
                    <div className="flex items-center justify-between">
                        <p className="text-2xl font-bold">Connect new tool</p>
                        <div className="flex items-center gap-x-4">
                            <SheetClose className="cursor-pointer">
                                <Icon
                                    name="arrow-right"
                                    className="dark:inline hidden"
                                />
                                <Icon
                                    name="arrow-right-dark"
                                    className="dark:hidden inline"
                                />
                            </SheetClose>
                        </div>
                    </div>
                </SheetHeader>
                <Form {...form}>
                    <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6 overflow-auto pb-12"
                    >
                        <FormField
                            control={form.control}
                            name="tool_config"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-lg">
                                        MCP Connect JSON
                                    </FormLabel>
                                    <FormControl>
                                        <div className="space-y-2 relative">
                                            <Icon
                                                name="key-square"
                                                className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                            />
                                            <Textarea
                                                id="tool-config"
                                                className="pl-[56px] min-h-[144px] mb-4"
                                                placeholder="Enter MCP Connect JSON"
                                                {...field}
                                            />
                                        </div>
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <div className="space-y-4 grid grid-cols-2 gap-4">
                            <Button
                                type="button"
                                variant="outline"
                                className="h-12 rounded-xl text-base"
                                onClick={
                                    mcpSettings.length > 0
                                        ? handleDelete
                                        : () => onOpenChange(false)
                                }
                            >
                                {mcpSettings.length > 0 ? 'Delete' : 'Cancel'}
                            </Button>
                            <Button
                                type="submit"
                                className="h-12 rounded-xl bg-sky-blue text-black text-base"
                                disabled={isLoading}
                            >
                                {isLoading
                                    ? 'Connecting...'
                                    : mcpSettings.length > 0
                                      ? 'Update'
                                      : 'Connect'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </SheetContent>
        </Sheet>
    )
}

export default ConnectToolMCP
