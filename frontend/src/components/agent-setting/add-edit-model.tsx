import { useState, useEffect } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { Loader2 } from 'lucide-react'

import { Icon } from '../ui/icon'
import { Sheet, SheetClose, SheetContent, SheetHeader } from '../ui/sheet'
import { PROVIDER_MODELS, PROVIDERS_NAME } from '@/constants/models'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '../ui/select'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from '../ui/form'
import { IModel } from '@/typings/settings'
import { settingsService } from '@/services/settings.service'
import { useAppSelector } from '@/state/store'

interface AddEditModelProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    onSaveConfig: (model: IModel, isEdit: boolean) => Promise<void>
    editingModel?: IModel | null
}

// const isCotModels = ['o3', 'o3-mini', 'o3-pro', 'o4-mini']

const FormSchema = z.object({
    model: z.object({
        model: z.string(),
        api_type: z.string()
    }),
    custom_model_name: z.string().optional(),
    api_key: z.string().optional(),
    azure_endpoint: z.string().optional(),
    azure_api_version: z.string().optional(),
    vertex_region: z.string().optional(),
    vertex_project_id: z.string().optional(),
    base_url: z.string().optional()
})

const AddEditModel = ({
    open,
    onOpenChange,
    onSaveConfig,
    editingModel
}: AddEditModelProps) => {
    const isEditing = !!editingModel
    const [selectedProvider, setSelectedProvider] = useState('anthropic')
    const [isSaving, setIsSaving] = useState(false)
    const [editModelData, setEditModelData] = useState<IModel>()

    const currentSettingData = useAppSelector(
        (state) => state.settings.currentSettingData
    )

    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues: {
            model: PROVIDER_MODELS.anthropic[0]
        }
    })

    const selectedModel = useWatch({ control: form.control, name: 'model' })

    const handleProviderChange = (provider: string) => {
        setSelectedProvider(provider)
        form.setValue(
            'model',
            PROVIDER_MODELS[provider as keyof typeof PROVIDER_MODELS][0]
        )
        form.setValue('custom_model_name', '')
    }

    const onSubmit = async (data: z.infer<typeof FormSchema>) => {
        const model = data.model as IModel
        const customName = data.custom_model_name || ''

        // Create model object
        const modelData: IModel = {
            id: isEditing && editingModel ? editingModel?.id : '',
            model: model.model === 'custom' ? customName : model.model,
            api_type: selectedProvider as 'openai' | 'anthropic' | 'google',
            source: 'user'
        }

        const additionalConfig: {
            api_key?: string
            base_url?: string
        } = {}

        if (selectedProvider === 'anthropic' && data.api_key) {
            additionalConfig.api_key = data.api_key
        } else if (selectedProvider === 'openai') {
            if (data.api_key) additionalConfig.api_key = data.api_key
            if (data.base_url) additionalConfig.base_url = data.base_url
        } else if (selectedProvider === 'google' && data.api_key) {
            additionalConfig.api_key = data.api_key
        }

        const finalModelData = { ...modelData, ...additionalConfig }

        try {
            setIsSaving(true)
            if (editingModel) {
                const res = await settingsService.updateModel(
                    editingModel?.id,
                    finalModelData
                )
                await onSaveConfig(res, isEditing)
            } else {
                const res = await settingsService.createModel(finalModelData)
                await onSaveConfig(res, false)
            }
            onOpenChange(false)
        } catch (error) {
            console.error('Error saving model:', error)
        } finally {
            setIsSaving(false)
        }
    }

    useEffect(() => {
        if (editingModel) {
            ;(async () => {
                const config = await settingsService.getModelById(
                    editingModel?.id
                )
                setSelectedProvider(config?.api_type)
                setEditModelData(config)
                let modelObj: { model: string; api_type: string } | undefined =
                    PROVIDER_MODELS[
                        config?.api_type as keyof typeof PROVIDER_MODELS
                    ]?.find((m) => m.model === config?.model)
                if (!modelObj) {
                    modelObj = {
                        model: 'custom',
                        api_type: config.api_type
                    }
                    form.setValue('custom_model_name', config.model)
                }

                form.setValue('model', modelObj)
                form.setValue('api_key', config.api_key || '')
                form.setValue('base_url', config.base_url || '')
            })()
        } else {
            setSelectedProvider('anthropic')
            form.reset({
                model: PROVIDER_MODELS.anthropic[0]
            })
        }
    }, [isEditing, editingModel, currentSettingData, form])

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="pt-12 w-full !max-w-[480px]">
                <SheetHeader className="px-6 pt-0 gap-1 pb-4">
                    <div className="flex items-center justify-between">
                        <p className="text-2xl font-bold">
                            {isEditing
                                ? 'Edit Model Configuration'
                                : 'Add New Model'}
                        </p>
                        <div className="flex items-center gap-x-4">
                            <SheetClose className="cursor-pointer">
                                <Icon
                                    name="arrow-right"
                                    className="dark:inline hidden"
                                />
                                <Icon
                                    name="arrow-right-dark"
                                    className="dark:hidden inline"
                                />
                            </SheetClose>
                        </div>
                    </div>
                    <p className="text-sm text-black/[0.56] dark:text-white/[0.56]">
                        Configure your LLM provider and API settings.
                    </p>
                </SheetHeader>
                <Form {...form}>
                    <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6 px-6 overflow-auto pb-12"
                    >
                        <div className="space-y-4">
                            <p className="text-lg font-bold dark:text-white">
                                LLM Provider
                            </p>
                            <div className="grid grid-cols-3 gap-4">
                                {['anthropic', 'openai', 'google'].map(
                                    (provider) => (
                                        <div
                                            key={provider}
                                            className={`h-[120px] flex items-center flex-col gap-4 rounded-2xl cursor-pointer ${
                                                selectedProvider === provider
                                                    ? 'border-2 border-firefly dark:border-sky-blue-2 bg-sky-blue dark:bg-sky-blue-2/20 p-[14px] font-bold'
                                                    : 'bg-firefly/5 dark:bg-sky-blue-2/5 p-4'
                                            }`}
                                            onClick={() => {
                                                handleProviderChange(provider)
                                            }}
                                        >
                                            <img
                                                src={`/images/${provider}.svg`}
                                                alt={provider}
                                                className={`w-auto h-[53px] ${
                                                    provider === 'anthropic' ||
                                                    provider === 'openai'
                                                        ? 'hidden dark:inline'
                                                        : ''
                                                }`}
                                            />
                                            {(provider === 'anthropic' ||
                                                provider === 'openai') && (
                                                <img
                                                    src={`/images/${provider}-dark.svg`}
                                                    alt={provider}
                                                    className="w-auto h-[53px] inline dark:hidden"
                                                />
                                            )}
                                            <p className="text-sm dark:text-white">
                                                {PROVIDERS_NAME[provider]}
                                            </p>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                        <FormField
                            control={form.control}
                            name="model"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-lg">
                                        Model Name
                                    </FormLabel>
                                    <FormControl>
                                        <Select
                                            onValueChange={(value) => {
                                                const model = PROVIDER_MODELS[
                                                    selectedProvider as keyof typeof PROVIDER_MODELS
                                                ].find((m) => m.model === value)
                                                if (model) {
                                                    field.onChange(model)
                                                }
                                            }}
                                            value={field.value?.model}
                                        >
                                            <SelectTrigger className="w-full **:fill-black **:dark:fill-white">
                                                <div className="flex items-center gap-4">
                                                    <SelectValue placeholder="Select Model" />
                                                </div>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {PROVIDER_MODELS[
                                                    selectedProvider as keyof typeof PROVIDER_MODELS
                                                ].map((model) => (
                                                    <SelectItem
                                                        key={model.model}
                                                        value={model.model}
                                                    >
                                                        <div className="flex items-center gap-4">
                                                            <Icon
                                                                name="cpu"
                                                                className={`size-6 fill-black`}
                                                            />
                                                            {model.model}
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <div className="space-y-4">
                            {selectedModel.model === 'custom' && (
                                <FormField
                                    control={form.control}
                                    name="custom_model_name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="cpu"
                                                        className="absolute top-3 left-4 fill-white"
                                                    />
                                                    <Input
                                                        id="custom-model-name"
                                                        className="pl-[56px]"
                                                        type="text"
                                                        placeholder="Enter custom model name"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            )}
                        </div>
                        {selectedProvider === 'anthropic' && (
                            <FormField
                                control={form.control}
                                name="api_key"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-lg">
                                            API Key
                                        </FormLabel>
                                        <FormControl>
                                            <div className="space-y-2 relative">
                                                <Icon
                                                    name="key-square"
                                                    className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                />
                                                <Input
                                                    id="api-key"
                                                    className="pl-[56px]"
                                                    type="password"
                                                    placeholder="Enter your API Key"
                                                    {...field}
                                                />
                                            </div>
                                        </FormControl>
                                        {isEditing && (
                                            <FormMessage className="mt-1 pl-4">
                                                <div className="flex gap-x-2 items-center">
                                                    <span className="font-bold text-sm italic">
                                                        Latest update
                                                    </span>
                                                    <span>
                                                        {dayjs().format(
                                                            'DD/MM/YYYY -- HH:mm:ss'
                                                        )}
                                                    </span>
                                                </div>
                                            </FormMessage>
                                        )}
                                    </FormItem>
                                )}
                            />
                        )}
                        {selectedProvider === 'openai' && (
                            <>
                                <FormField
                                    control={form.control}
                                    name="api_key"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-lg">
                                                API Key
                                            </FormLabel>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="key-square"
                                                        className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                    />
                                                    <Input
                                                        id="api-key"
                                                        className="pl-[56px]"
                                                        type="password"
                                                        placeholder="Enter your API Key"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                            {isEditing && (
                                                <FormMessage className="mt-1 pl-4">
                                                    <div className="flex gap-x-2 items-center">
                                                        <span className="font-bold text-sm italic">
                                                            Latest update
                                                        </span>
                                                        <span>
                                                            {dayjs().format(
                                                                'DD/MM/YYYY -- HH:mm:ss'
                                                            )}
                                                        </span>
                                                    </div>
                                                </FormMessage>
                                            )}
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="base_url"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-lg">
                                                Base URL (Optional)
                                            </FormLabel>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="link-2"
                                                        className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                    />
                                                    <Input
                                                        id="base-url"
                                                        className="pl-[56px]"
                                                        type="text"
                                                        placeholder="Enter your Base URL"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </>
                        )}
                        {selectedProvider === 'google' && (
                            <FormField
                                control={form.control}
                                name="api_key"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-lg">
                                            API Key
                                        </FormLabel>
                                        <FormControl>
                                            <div className="space-y-2 relative">
                                                <Icon
                                                    name="key-square"
                                                    className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                />
                                                <Input
                                                    id="api-key"
                                                    className="pl-[56px]"
                                                    type="password"
                                                    placeholder="Enter your API Key"
                                                    {...field}
                                                />
                                            </div>
                                        </FormControl>
                                        {isEditing && (
                                            <FormMessage className="mt-1 pl-4">
                                                <div className="flex gap-x-2 items-center">
                                                    <span className="font-bold text-sm italic">
                                                        Latest update
                                                    </span>
                                                    <span>
                                                        {dayjs().format(
                                                            'DD/MM/YYYY -- HH:mm:ss'
                                                        )}
                                                    </span>
                                                </div>
                                            </FormMessage>
                                        )}
                                    </FormItem>
                                )}
                            />
                        )}
                        {selectedProvider === 'vertex' && (
                            <>
                                <FormField
                                    control={form.control}
                                    name="vertex_project_id"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-lg">
                                                Vertex Project ID
                                            </FormLabel>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="folder-2"
                                                        className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                    />
                                                    <Input
                                                        id="vertex-project-id"
                                                        className="pl-[56px]"
                                                        type="text"
                                                        placeholder="Enter your Vertex Project ID"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="vertex_region"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-lg">
                                                Vertex Region
                                            </FormLabel>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="global"
                                                        className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                    />
                                                    <Input
                                                        id="vertex-region"
                                                        className="pl-[56px]"
                                                        type="text"
                                                        placeholder="Enter your Vertex Region"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </>
                        )}

                        {selectedProvider === 'azure' && (
                            <>
                                <FormField
                                    control={form.control}
                                    name="azure_endpoint"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-lg">
                                                Azure Endpoint
                                            </FormLabel>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="cd"
                                                        className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                    />
                                                    <Input
                                                        id="azure-endpoint"
                                                        className="pl-[56px]"
                                                        type="text"
                                                        placeholder="Enter your Azure Endpoint"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="api_key"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-lg">
                                                API Key
                                            </FormLabel>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="key-square"
                                                        className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                    />
                                                    <Input
                                                        id="api-key"
                                                        className="pl-[56px]"
                                                        type="password"
                                                        placeholder="Enter your API Key"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                            {isEditing && (
                                                <FormMessage className="mt-1 pl-4">
                                                    <div className="flex gap-x-2 items-center">
                                                        <span className="font-bold text-sm italic">
                                                            Latest update
                                                        </span>
                                                        <span>
                                                            {dayjs(
                                                                editModelData?.updated_at
                                                            ).format(
                                                                'DD/MM/YYYY -- HH:mm:ss'
                                                            )}
                                                        </span>
                                                    </div>
                                                </FormMessage>
                                            )}
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="azure_api_version"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-lg">
                                                Azure API Version
                                            </FormLabel>
                                            <FormControl>
                                                <div className="space-y-2 relative">
                                                    <Icon
                                                        name="tag-2"
                                                        className={`absolute top-3 left-4 fill-black dark:fill-white ${field.value ? '' : 'opacity-30'}`}
                                                    />
                                                    <Input
                                                        id="azure-api-version"
                                                        className="pl-[56px]"
                                                        type="text"
                                                        placeholder="Enter your Azure API Version"
                                                        {...field}
                                                    />
                                                </div>
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </>
                        )}
                        <div className="grid grid-cols-2 gap-4 mt-12">
                            <Button
                                type="reset"
                                variant="outline"
                                className="col-span-1 h-12 rounded-xl text-base"
                                onClick={() => onOpenChange(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                className="col-span-1 h-12 rounded-xl bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black text-base"
                                disabled={isSaving}
                            >
                                {isSaving && (
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                )}
                                Save
                            </Button>
                        </div>
                    </form>
                </Form>
            </SheetContent>
        </Sheet>
    )
}

export default AddEditModel
