'use client'

import { useEffect, useState } from 'react'
import { Link, useNavigate, useParams } from 'react-router'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger
} from './ui/collapsible'
import { Icon } from './ui/icon'
import {
    Sidebar as SidebarContainer,
    SidebarContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuItem
} from './ui/sidebar'
import {
    setCompleted,
    setMessages,
    fetchSessions,
    setActiveSessionId,
    selectSessions,
    selectActiveSessionId,
    useAppDispatch,
    useAppSelector,
    setBuildStep,
    setCurrentActionData,
    setPlans,
    setAgentInitialized
} from '@/state'
import SearchHistory from './search-history'
import { BUILD_STEP } from '@/typings/agent'

interface SidebarButtonProps {
    className?: string
    workspaceInfo?: string
}

const Sidebar = ({ workspaceInfo }: SidebarButtonProps) => {
    const navigate = useNavigate()
    const [isCollapsibleOpen, setIsCollapsibleOpen] = useState(false)

    const dispatch = useAppDispatch()
    const sessions = useAppSelector(selectSessions)
    const activeSessionId = useAppSelector(selectActiveSessionId)
    const { sessionId } = useParams()

    const handleNewChat = () => {
        dispatch(setMessages([]))
        dispatch(setPlans([]))
        dispatch(setCompleted(false))
        dispatch(setBuildStep(BUILD_STEP.THINKING))
        dispatch(setAgentInitialized(false))
        navigate('/')
    }

    const handleResetState = () => {
        dispatch(setMessages([]))
        dispatch(setPlans([]))
        dispatch(setCompleted(false))
        dispatch(setBuildStep(BUILD_STEP.THINKING))
        dispatch(setAgentInitialized(false))
        dispatch(setCurrentActionData(undefined))
    }

    // Get the current session ID from URL parameters
    useEffect(() => {
        if (sessionId) {
            dispatch(setActiveSessionId(sessionId))
        }
    }, [sessionId, dispatch])

    const header = (
        <div className="flex items-center justify-center gap-4">
            {/* <Button
                variant="ghost"
                size="icon"
                className="p-0 size-auto"
                onClick={toggleSidebar}
            >
                <Icon name="sidebar" />
            </Button> */}
            <div className="flex items-center gap-3">
                <img
                    src="/images/logo-only.svg"
                    alt="Logo"
                    width={40}
                    height={40}
                    className="rounded-sm hidden dark:inline"
                />
                <img
                    src="/images/logo-charcoal.svg"
                    alt="Logo"
                    width={40}
                    height={40}
                    className="rounded-sm dark:hidden"
                />
                <span
                    className={`text-black dark:text-white text-2xl font-bold`}
                >
                    II-Agent
                </span>
            </div>
        </div>
    )

    useEffect(() => {
        dispatch(fetchSessions())
    }, [dispatch])

    return (
        <SidebarContainer className="bg-[#f8fafb]/30 dark:bg-charcoal !border-grey-2/30 dark:!border-grey/30">
            <SidebarHeader>{header}</SidebarHeader>
            <SidebarContent>
                <SidebarMenu>
                    <div className="px-6 pb-6">
                        <Button
                            className="bg-firefly dark:bg-sky-blue w-full !text-sky-blue dark:!text-black"
                            size="xl"
                            onClick={handleNewChat}
                        >
                            <Icon
                                name="edit"
                                className="fill-sky-blue dark:fill-black"
                            />{' '}
                            New chat
                        </Button>
                        <SearchHistory className="mt-4" />
                        <SidebarMenuItem className="mt-4">
                            <Link
                                to="/dashboard"
                                className="flex items-center gap-x-2 px-4"
                            >
                                <Icon
                                    name="dashboard"
                                    className="fill-black dark:fill-white"
                                />
                                Dashboard
                            </Link>
                        </SidebarMenuItem>

                        <div className="mt-8 space-y-8">
                            {/* <Button
                                variant="outline"
                                className="w-full justify-start !h-9 !text-[14px] !px-4 rounded-xl"
                            >
                                <Icon
                                    name="folder-add"
                                    className="size-5 fill-black dark:fill-white"
                                />{' '}
                                New Project
                            </Button> */}
                            <Collapsible onOpenChange={setIsCollapsibleOpen}>
                                <CollapsibleTrigger className="w-full">
                                    <div className="w-full justify-start !h-9 !text-[14px] !px-4 rounded-xl cursor-pointer border border-black dark:border-white flex items-center">
                                        <div className="flex items-center gap-x-2 flex-1">
                                            <Icon
                                                name="message-minus"
                                                className="size-5 fill-black dark:fill-white"
                                            />{' '}
                                            Single Chat
                                        </div>
                                        <Icon
                                            name="arrow-down"
                                            className={`size-5 fill-black dark:fill-white transition-transform duration-200 ${
                                                isCollapsibleOpen
                                                    ? 'rotate-180'
                                                    : ''
                                            }`}
                                        />
                                    </div>
                                </CollapsibleTrigger>
                                <CollapsibleContent className="mt-3">
                                    <div className="space-y-4 pl-4 text-[14px]">
                                        {sessions
                                            ?.filter((session) => session.name)
                                            ?.map((session) => (
                                                <Link
                                                    key={session.id}
                                                    to={`/${session.id}`}
                                                    onClick={handleResetState}
                                                    className={cn(
                                                        'flex items-center gap-x-2 line-clamp-1 dark:hover:text-sky-blue',
                                                        activeSessionId ===
                                                            session.id ||
                                                            workspaceInfo?.includes(
                                                                session.id
                                                            )
                                                            ? 'font-bold'
                                                            : ''
                                                    )}
                                                >
                                                    {session.name}
                                                </Link>
                                            ))}
                                    </div>
                                </CollapsibleContent>
                            </Collapsible>
                        </div>
                    </div>
                </SidebarMenu>
            </SidebarContent>
        </SidebarContainer>
    )
}

export default Sidebar
