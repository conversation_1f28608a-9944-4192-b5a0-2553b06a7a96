import { useTheme } from 'next-themes'
import { useNavigate } from 'react-router'
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar'
import { Icon } from './ui/icon'
import ButtonIcon from './button-icon'
import { WebSocketConnectionState } from '@/typings/agent'
import { useAppSelector } from '@/state/store'
import { selectUser } from '@/state/slice/user'
import { getFirstCharacters } from '@/lib/utils'

const RightSidebar = () => {
    const { theme, setTheme } = useTheme()
    const navigate = useNavigate()
    const wsConnectionState = useAppSelector(
        (state) => state.agent.wsConnectionState
    )
    const user = useAppSelector(selectUser)

    const toggleTheme = () => {
        setTheme(theme === 'dark' ? 'light' : 'dark')
    }

    const handleAvatarClick = () => {
        navigate('/settings')
    }

    return (
        <div className="flex items-center justify-between flex-col h-full py-8 px-6 border-l border-neutral-200 dark:border-sidebar-border">
            <div className="flex flex-col items-center gap-4">
                <Avatar 
                    className="size-10 cursor-pointer hover:opacity-80 transition-opacity" 
                    onClick={handleAvatarClick}
                >
                    <AvatarImage src={user?.avatar} />
                    <AvatarFallback>
                        {user?.first_name
                            ? getFirstCharacters(
                                  `${user?.first_name} ${user?.last_name}`
                              )
                            : `II`}
                    </AvatarFallback>
                </Avatar>

                {/* <ButtonIcon name="notification" />
                <ButtonIcon name="info-circle" /> */}
                <ButtonIcon
                    name={theme === 'dark' ? 'sun' : 'moon'}
                    iconClassName="!fill-none"
                    className="border border-black"
                    onClick={toggleTheme}
                />
            </div>
            {wsConnectionState === WebSocketConnectionState.CONNECTED && (
                <Icon name="connected" />
            )}
        </div>
    )
}

export default RightSidebar
