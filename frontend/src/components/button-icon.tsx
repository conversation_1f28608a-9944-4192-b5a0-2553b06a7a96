import { Button } from './ui/button'
import { Icon } from './ui/icon'

interface ButtonIconProps {
    name: string
    disabled?: boolean
    className?: string
    iconClassName?: string
    onClick?: () => void
}

const ButtonIcon = ({
    name,
    className,
    iconClassName,
    onClick,
    disabled
}: ButtonIconProps) => {
    return (
        <Button
            variant="secondary"
            size="icon"
            className={`size-7 bg-white dark:bg-sky-blue rounded-full cursor-pointer ${className}`}
            onClick={onClick}
            disabled={disabled}
        >
            <Icon
                name={name}
                className={`size-[18px] fill-black ${iconClassName}`}
            />
        </Button>
    )
}

export default ButtonIcon
