import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export interface User {
    id: string
    first_name: string
    last_name: string
    email: string
    avatar?: string
}

interface UserState {
    user: User | null
    isLoading: boolean
}

const initialState: UserState = {
    user: null,
    isLoading: true
}

const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        setUser: (state, action: PayloadAction<User>) => {
            state.user = action.payload
            state.isLoading = false
        },
        clearUser: (state) => {
            state.user = null
            state.isLoading = false
        },
        setLoading: (state, action: PayloadAction<boolean>) => {
            state.isLoading = action.payload
        }
    }
})

export const { setUser, clearUser, setLoading } = userSlice.actions
export const userReducer = userSlice.reducer

// Selectors
export const selectUser = (state: { user: UserState }) => state.user.user
