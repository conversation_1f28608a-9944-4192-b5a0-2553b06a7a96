import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { ISession } from '@/typings/agent'
import { sessionService } from '@/services/session.service'

interface SessionsState {
    sessions: ISession[]
    activeSessionId: string | null
    isLoading: boolean
    error: string | null
}

const initialState: SessionsState = {
    sessions: [],
    activeSessionId: null,
    isLoading: false,
    error: null
}

export const fetchSessions = createAsyncThunk(
    'sessions/fetchSessions',
    async () => {
        return await sessionService.getSessions()
    }
)

const sessionsSlice = createSlice({
    name: 'sessions',
    initialState,
    reducers: {
        setActiveSessionId: (state, action: PayloadAction<string | null>) => {
            state.activeSessionId = action.payload
        },
        clearSessions: (state) => {
            state.sessions = []
            state.activeSessionId = null
            state.error = null
        },
        clearError: (state) => {
            state.error = null
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchSessions.pending, (state) => {
                state.isLoading = true
                state.error = null
            })
            .addCase(fetchSessions.fulfilled, (state, action) => {
                state.isLoading = false
                state.sessions = action.payload
            })
            .addCase(fetchSessions.rejected, (state, action) => {
                state.isLoading = false
                state.error = action.error.message || 'Failed to fetch sessions'
            })
    }
})

export const { setActiveSessionId, clearSessions, clearError } =
    sessionsSlice.actions
export const sessionsReducer = sessionsSlice.reducer

export const selectSessions = (state: { sessions: SessionsState }) =>
    state.sessions.sessions
export const selectActiveSessionId = (state: { sessions: SessionsState }) =>
    state.sessions.activeSessionId
export const selectSessionsLoading = (state: { sessions: SessionsState }) =>
    state.sessions.isLoading
export const selectSessionsError = (state: { sessions: SessionsState }) =>
    state.sessions.error
