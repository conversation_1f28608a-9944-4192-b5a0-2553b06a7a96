import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { ActionStep } from '@/typings/agent'

interface EditorState {
    activeFileCodeEditor: string
    currentActionData?: ActionStep
}

const initialState: EditorState = {
    activeFileCodeEditor: '',
    currentActionData: undefined
}

const editorSlice = createSlice({
    name: 'editor',
    initialState,
    reducers: {
        setActiveFile: (state, action: PayloadAction<string>) => {
            state.activeFileCodeEditor = action.payload
        },
        setCurrentActionData: (state, action: PayloadAction<ActionStep | undefined>) => {
            state.currentActionData = action.payload
        }
    }
})

export const { setActiveFile, setCurrentActionData } = editorSlice.actions
export const editorReducer = editorSlice.reducer

// Selectors
export const selectActiveFileCodeEditor = (state: { editor: EditorState }) => state.editor.activeFileCodeEditor
export const selectCurrentActionData = (state: { editor: EditorState }) => state.editor.currentActionData